#!/usr/bin/env python3
"""
Quotex Candle Data Provider
Alternative solution for getting market data when WebSocket is blocked
"""

import asyncio
import time
import json
import requests
from datetime import datetime, timedelta
import pandas as pd

class CandleDataProvider:
    """
    Provides candle data using alternative sources when Quotex WebSocket is blocked
    """
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
    def quotex_to_standard_symbol(self, quotex_asset):
        """Convert Quotex asset names to standard symbols"""
        symbol_map = {
            'EURUSD_otc': 'EURUSD',
            'GBPUSD_otc': 'GBPUSD',
            'USDJPY_otc': 'USDJPY',
            'AUDUSD_otc': 'AUDUSD',
            'USDCAD_otc': 'USDCAD',
            'USDCHF_otc': 'USDCHF',
            'NZDUSD_otc': 'NZDUSD',
            'EURJPY_otc': 'EURJPY',
            'GBPJPY_otc': 'GBPJPY',
            'EURGBP_otc': 'EURGBP',
            'AUDCAD_otc': 'AUDCAD',
            'AUDCHF_otc': 'AUDCHF',
            'AUDJPY_otc': 'AUDJPY',
            'CADCHF_otc': 'CADCHF',
            'CADJPY_otc': 'CADJPY',
            'CHFJPY_otc': 'CHFJPY',
            'EURAUD_otc': 'EURAUD',
            'EURCAD_otc': 'EURCAD',
            'EURCHF_otc': 'EURCHF',
            'EURNZD_otc': 'EURNZD',
            'GBPAUD_otc': 'GBPAUD',
            'GBPCAD_otc': 'GBPCAD',
            'GBPCHF_otc': 'GBPCHF',
            'GBPNZD_otc': 'GBPNZD',
            'NZDCAD_otc': 'NZDCAD',
            'NZDCHF_otc': 'NZDCHF',
            'NZDJPY_otc': 'NZDJPY'
        }
        
        # Remove _otc suffix if present
        clean_symbol = quotex_asset.replace('_otc', '')
        return symbol_map.get(quotex_asset, clean_symbol)
    
    async def get_candles_from_fcsapi(self, symbol, period='1m', count=100):
        """
        Get candle data from FCS API (free tier available)
        """
        try:
            # Convert period to FCS format
            period_map = {
                60: '1m',
                300: '5m',
                900: '15m',
                1800: '30m',
                3600: '1h',
                14400: '4h',
                86400: '1d'
            }
            
            if isinstance(period, int):
                period = period_map.get(period, '1m')
            
            # FCS API endpoint (you'll need to get a free API key)
            url = "https://fcsapi.com/api-v3/forex/candle"
            
            params = {
                'symbol': self.quotex_to_standard_symbol(symbol),
                'period': period,
                'limit': count,
                'access_key': 'YOUR_FCS_API_KEY'  # Replace with actual key
            }
            
            print(f"🔍 Fetching candles from FCS API for {symbol}")
            
            response = self.session.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') and data.get('response'):
                    candles = data['response']
                    print(f"✅ Retrieved {len(candles)} candles from FCS API")
                    return self._format_candles(candles, 'fcs')
                else:
                    print(f"❌ FCS API error: {data.get('msg', 'Unknown error')}")
            
            return []
            
        except Exception as e:
            print(f"❌ FCS API error: {e}")
            return []
    
    async def get_candles_from_alpha_vantage(self, symbol, period='1min', count=100):
        """
        Get candle data from Alpha Vantage (free tier available)
        """
        try:
            # Convert symbol for Alpha Vantage
            av_symbol = self.quotex_to_standard_symbol(symbol)
            
            url = "https://www.alphavantage.co/query"
            
            params = {
                'function': 'FX_INTRADAY',
                'from_symbol': av_symbol[:3],
                'to_symbol': av_symbol[3:],
                'interval': period,
                'apikey': 'YOUR_ALPHA_VANTAGE_KEY',  # Replace with actual key
                'outputsize': 'compact'
            }
            
            print(f"🔍 Fetching candles from Alpha Vantage for {symbol}")
            
            response = self.session.get(url, params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                time_series_key = f'Time Series FX ({period})'
                
                if time_series_key in data:
                    time_series = data[time_series_key]
                    candles = []
                    
                    for timestamp, ohlc in list(time_series.items())[:count]:
                        candles.append({
                            'timestamp': int(datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S').timestamp()),
                            'open': float(ohlc['1. open']),
                            'high': float(ohlc['2. high']),
                            'low': float(ohlc['3. low']),
                            'close': float(ohlc['4. close'])
                        })
                    
                    print(f"✅ Retrieved {len(candles)} candles from Alpha Vantage")
                    return candles
                else:
                    print(f"❌ Alpha Vantage error: {data.get('Error Message', 'Unknown error')}")
            
            return []
            
        except Exception as e:
            print(f"❌ Alpha Vantage error: {e}")
            return []
    
    async def get_candles_mock_data(self, symbol, period=60, count=100):
        """
        Generate mock candle data for testing when APIs are not available
        """
        try:
            print(f"🎭 Generating mock candle data for {symbol}")
            
            import random
            
            # Base price for different symbols
            base_prices = {
                'EURUSD': 1.0850,
                'GBPUSD': 1.2650,
                'USDJPY': 149.50,
                'AUDUSD': 0.6450,
                'USDCAD': 1.3750,
                'USDCHF': 0.8950
            }
            
            clean_symbol = self.quotex_to_standard_symbol(symbol)
            base_price = base_prices.get(clean_symbol, 1.0000)
            
            candles = []
            current_time = int(time.time())
            current_price = base_price
            
            for i in range(count):
                timestamp = current_time - (count - i) * period
                
                # Generate realistic price movement
                change_percent = random.uniform(-0.002, 0.002)  # ±0.2% change
                price_change = current_price * change_percent
                
                open_price = current_price
                close_price = current_price + price_change
                
                # Generate high and low
                high_price = max(open_price, close_price) + abs(price_change) * random.uniform(0, 0.5)
                low_price = min(open_price, close_price) - abs(price_change) * random.uniform(0, 0.5)
                
                candles.append({
                    'timestamp': timestamp,
                    'open': round(open_price, 5),
                    'high': round(high_price, 5),
                    'low': round(low_price, 5),
                    'close': round(close_price, 5),
                    'volume': random.randint(1000, 10000)
                })
                
                current_price = close_price
            
            print(f"✅ Generated {len(candles)} mock candles")
            return candles
            
        except Exception as e:
            print(f"❌ Mock data error: {e}")
            return []
    
    def _format_candles(self, candles, source='generic'):
        """Format candles to standard format"""
        formatted = []
        
        for candle in candles:
            if source == 'fcs':
                formatted.append({
                    'timestamp': int(candle.get('tm', 0)),
                    'open': float(candle.get('o', 0)),
                    'high': float(candle.get('h', 0)),
                    'low': float(candle.get('l', 0)),
                    'close': float(candle.get('c', 0))
                })
            else:
                formatted.append(candle)
        
        return formatted
    
    async def get_candles(self, symbol, period=60, count=100):
        """
        Get candle data using the best available method
        """
        print(f"📊 Getting candle data for {symbol} ({period}s period, {count} candles)")
        
        # Try different sources in order of preference
        methods = [
            # self.get_candles_from_fcsapi,      # Uncomment when you have API key
            # self.get_candles_from_alpha_vantage, # Uncomment when you have API key
            self.get_candles_mock_data           # Always available for testing
        ]
        
        for method in methods:
            try:
                candles = await method(symbol, period, count)
                if candles:
                    return candles
            except Exception as e:
                print(f"❌ Method {method.__name__} failed: {e}")
                continue
        
        print("❌ All candle data sources failed")
        return []
    
    def analyze_candles(self, candles):
        """
        Analyze candle data and provide trading insights
        """
        if not candles or len(candles) < 2:
            return {}
        
        try:
            # Convert to DataFrame for analysis
            df = pd.DataFrame(candles)
            
            # Calculate basic indicators
            df['sma_5'] = df['close'].rolling(window=5).mean()
            df['sma_20'] = df['close'].rolling(window=20).mean()
            
            # Price change analysis
            latest = candles[-1]
            previous = candles[-2]
            
            price_change = latest['close'] - previous['close']
            price_change_percent = (price_change / previous['close']) * 100
            
            # Trend analysis
            recent_closes = [c['close'] for c in candles[-5:]]
            trend = "UP" if recent_closes[-1] > recent_closes[0] else "DOWN"
            
            # Support and resistance levels
            highs = [c['high'] for c in candles[-20:]]
            lows = [c['low'] for c in candles[-20:]]
            
            resistance = max(highs) if highs else latest['high']
            support = min(lows) if lows else latest['low']
            
            analysis = {
                'latest_price': latest['close'],
                'price_change': price_change,
                'price_change_percent': price_change_percent,
                'trend': trend,
                'support': support,
                'resistance': resistance,
                'sma_5': df['sma_5'].iloc[-1] if len(df) >= 5 else None,
                'sma_20': df['sma_20'].iloc[-1] if len(df) >= 20 else None,
                'volatility': df['close'].std() if len(df) > 1 else 0
            }
            
            return analysis
            
        except Exception as e:
            print(f"❌ Analysis error: {e}")
            return {}

# Test the candle data provider
async def test_candle_provider():
    """Test the candle data provider"""
    
    provider = CandleDataProvider()
    
    # Test assets
    test_assets = ['EURUSD_otc', 'GBPUSD_otc', 'USDJPY_otc']
    
    for asset in test_assets:
        print(f"\n🔄 Testing {asset}")
        
        # Get candle data
        candles = await provider.get_candles(asset, period=60, count=20)
        
        if candles:
            print(f"📊 Retrieved {len(candles)} candles")
            print(f"📈 Latest candle: {candles[-1]}")
            
            # Analyze the data
            analysis = provider.analyze_candles(candles)
            
            if analysis:
                print(f"💹 Analysis:")
                print(f"   Price: {analysis.get('latest_price', 'N/A')}")
                print(f"   Change: {analysis.get('price_change_percent', 0):.3f}%")
                print(f"   Trend: {analysis.get('trend', 'N/A')}")
                print(f"   Support: {analysis.get('support', 'N/A')}")
                print(f"   Resistance: {analysis.get('resistance', 'N/A')}")
        else:
            print("❌ No candle data retrieved")

if __name__ == "__main__":
    asyncio.run(test_candle_provider())
