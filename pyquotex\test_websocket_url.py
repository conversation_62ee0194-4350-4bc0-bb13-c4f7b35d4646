#!/usr/bin/env python3
"""
Test the WebSocket URL and connection approach
"""

import asyncio
import websocket
import ssl
import time
from pyquotex.http.login import Login

async def test_websocket_connection():
    """Test different WebSocket connection approaches"""
    
    # First, authenticate via HTTP to get cookies
    print("🔐 Authenticating via HTTP...")
    
    api_mock = type('obj', (object,), {
        'lang': 'pt',
        'https_url': 'https://market-qx.pro',
        'host': 'market-qx.pro',
        'resource_path': '.',
        'user_data_dir': '.',
        'username': '<EMAIL>',
        'password': 'Uz2309##2309',
        'session_data': {
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'cookies': None,
            'token': None
        }
    })()
    
    login_module = Login(api_mock)
    status, message = await login_module('<EMAIL>', 'Uz2309##2309')
    
    if not status:
        print(f"❌ HTTP authentication failed: {message}")
        return
    
    print("✅ HTTP authentication successful!")
    
    # Get cookies
    cookies = None
    if hasattr(login_module, 'response') and login_module.response:
        cookies = login_module.response.cookies
        cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
        api_mock.session_data["cookies"] = cookie_string
        print(f"🍪 Got cookies: {cookie_string[:100]}...")
    
    # Test different WebSocket URLs
    test_urls = [
        "wss://ws2.market-qx.pro/socket.io/?EIO=3&transport=websocket",
        "wss://ws2.market-qx.pro/socket.io/?EIO=4&transport=websocket", 
        "wss://ws2.market-qx.pro/",
        "wss://ws2.market-qx.pro/websocket",
    ]
    
    headers = {
        "User-Agent": api_mock.session_data["user_agent"],
        "Origin": "https://market-qx.pro",
        "Cookie": api_mock.session_data.get("cookies", ""),
        "Sec-WebSocket-Extensions": "permessage-deflate; client_max_window_bits",
        "Sec-WebSocket-Version": "13"
    }
    
    for url in test_urls:
        print(f"\n🔍 Testing URL: {url}")
        
        try:
            # Create WebSocket connection
            def on_message(ws, message):
                print(f"📨 Received: {message[:200]}...")
            
            def on_error(ws, error):
                print(f"❌ Error: {error}")
            
            def on_close(ws, close_status_code, close_msg):
                print(f"🔌 Connection closed: {close_status_code} - {close_msg}")
            
            def on_open(ws):
                print("✅ WebSocket connection opened!")
                
                # Send Socket.IO handshake messages
                try:
                    # Socket.IO connection message
                    ws.send("40")  # Connect message
                    time.sleep(1)
                    
                    # Send authentication or ping
                    ws.send('42["tick"]')  # Tick message like PyQuotex does
                    
                except Exception as e:
                    print(f"❌ Send error: {e}")
            
            # Create SSL context
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            # Create WebSocket
            ws = websocket.WebSocketApp(
                url,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close,
                on_open=on_open,
                header=headers,
                cookie=api_mock.session_data.get("cookies")
            )
            
            # Run with timeout using threading
            print("⏳ Attempting connection...")

            import threading

            def run_websocket():
                ws.run_forever(
                    sslopt={
                        "cert_reqs": ssl.CERT_NONE,
                        "check_hostname": False,
                        "context": ssl_context
                    },
                    ping_interval=30,
                    ping_timeout=10
                )

            # Run in thread with timeout
            thread = threading.Thread(target=run_websocket)
            thread.daemon = True
            thread.start()
            thread.join(timeout=15)  # 15 second timeout

            if thread.is_alive():
                print("⏰ Connection timeout")
                ws.close()
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    asyncio.run(test_websocket_connection())
