#!/usr/bin/env python3
"""
Quotex Trading Bot with WebSocket Support
Handles trading, candle data, and signal processing
"""

import asyncio
import time
import json
import logging
from datetime import datetime
from pyquotex.stable_api import Quotex

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuotexTradingBot:
    def __init__(self, email, password, lang="pt", demo_mode=True):
        self.email = email
        self.password = password
        self.lang = lang
        self.demo_mode = demo_mode
        self.client = None
        self.is_connected = False
        self.connection_attempts = 0
        self.max_connection_attempts = 10
        
    async def connect(self):
        """Connect to Quotex with retry logic"""
        print("🚀 Starting Quotex Trading Bot...")
        
        while self.connection_attempts < self.max_connection_attempts:
            try:
                self.connection_attempts += 1
                print(f"🔌 Connection attempt {self.connection_attempts}/{self.max_connection_attempts}")
                
                # Create client
                self.client = Quotex(
                    email=self.email,
                    password=self.password,
                    lang=self.lang
                )
                
                # Enable debug mode
                self.client.debug_ws_enable = True
                
                # Attempt connection
                check_connect, message = await self.client.connect()
                
                if check_connect:
                    print("✅ Connected to Quotex successfully!")
                    self.is_connected = True
                    
                    # Set account mode
                    if self.demo_mode:
                        await self.client.change_account("PRACTICE")
                        print("🔵 Switched to Demo account")
                    else:
                        await self.client.change_account("REAL")
                        print("🔴 Switched to Live account")
                    
                    # Get initial balance
                    balance = await self.get_balance()
                    print(f"💰 Current balance: ${balance:.2f}")
                    
                    return True
                else:
                    print(f"❌ Connection failed: {message}")
                    if self.connection_attempts < self.max_connection_attempts:
                        print(f"⏳ Retrying in 10 seconds...")
                        await asyncio.sleep(10)
                    
            except Exception as e:
                print(f"❌ Connection error: {e}")
                if self.connection_attempts < self.max_connection_attempts:
                    print(f"⏳ Retrying in 10 seconds...")
                    await asyncio.sleep(10)
        
        print("💥 Failed to connect after maximum attempts")
        return False
    
    async def get_balance(self):
        """Get current account balance"""
        try:
            if not self.is_connected:
                raise Exception("Not connected to Quotex")
            
            balance = await self.client.get_balance()
            return balance
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return 0.0
    
    async def place_trade(self, asset, direction, amount, duration):
        """
        Place a trade on Quotex
        
        Args:
            asset (str): Asset symbol (e.g., "EURUSD", "EURUSD_otc")
            direction (str): "call" for up, "put" for down
            amount (float): Trade amount
            duration (int): Trade duration in seconds
        
        Returns:
            dict: Trade result with status and details
        """
        try:
            if not self.is_connected:
                raise Exception("Not connected to Quotex")
            
            print(f"📈 Placing {direction.upper()} trade:")
            print(f"   Asset: {asset}")
            print(f"   Amount: ${amount}")
            print(f"   Duration: {duration}s")
            
            # Place the trade
            status, buy_info = await self.client.buy(amount, asset, direction, duration)
            
            if status:
                print("✅ Trade placed successfully!")
                print(f"   Trade ID: {buy_info.get('id', 'Unknown')}")
                return {
                    'success': True,
                    'trade_id': buy_info.get('id'),
                    'details': buy_info
                }
            else:
                print(f"❌ Trade failed: {buy_info}")
                return {
                    'success': False,
                    'error': buy_info
                }
                
        except Exception as e:
            logger.error(f"Error placing trade: {e}")
            print(f"❌ Trade error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_candles(self, asset, period=60, count=100):
        """
        Get historical candle data
        
        Args:
            asset (str): Asset symbol
            period (int): Candle period in seconds (60, 300, 900, etc.)
            count (int): Number of candles to fetch
        
        Returns:
            list: List of candle data
        """
        try:
            if not self.is_connected:
                raise Exception("Not connected to Quotex")
            
            print(f"📊 Fetching {count} candles for {asset} ({period}s period)")
            
            end_time = time.time()
            offset = count * period
            
            candles = await self.client.get_candles(asset, end_time, offset, period)
            
            if candles:
                print(f"✅ Retrieved {len(candles)} candles")
                return candles
            else:
                print("❌ No candles retrieved")
                return []
                
        except Exception as e:
            logger.error(f"Error getting candles: {e}")
            print(f"❌ Candle data error: {e}")
            return []
    
    async def get_realtime_price(self, asset):
        """Get real-time price for an asset"""
        try:
            if not self.is_connected:
                raise Exception("Not connected to Quotex")
            
            # Start price stream
            price_data = await self.client.start_realtime_price(asset)
            return price_data.get(asset, {})
            
        except Exception as e:
            logger.error(f"Error getting real-time price: {e}")
            return {}
    
    async def start_candle_stream(self, asset, period=60):
        """Start real-time candle stream"""
        try:
            if not self.is_connected:
                raise Exception("Not connected to Quotex")
            
            print(f"📡 Starting candle stream for {asset} ({period}s)")
            self.client.start_candles_stream(asset, period)
            return True
            
        except Exception as e:
            logger.error(f"Error starting candle stream: {e}")
            return False
    
    async def process_signal(self, signal):
        """
        Process a trading signal
        
        Args:
            signal (dict): Trading signal with asset, direction, amount, duration
        
        Returns:
            dict: Trade result
        """
        try:
            asset = signal.get('asset')
            direction = signal.get('direction')  # 'call' or 'put'
            amount = signal.get('amount', 10.0)
            duration = signal.get('duration', 60)
            
            if not asset or not direction:
                raise Exception("Invalid signal: missing asset or direction")
            
            print(f"🎯 Processing signal: {direction.upper()} on {asset}")
            
            # Place the trade based on signal
            result = await self.place_trade(asset, direction, amount, duration)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing signal: {e}")
            return {'success': False, 'error': str(e)}
    
    async def close(self):
        """Close the connection"""
        if self.client:
            await self.client.close()
            print("🔌 Connection closed")

# Example usage and testing
async def test_trading_bot():
    """Test the trading bot functionality"""
    
    # Initialize bot
    bot = QuotexTradingBot(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt",
        demo_mode=True  # Use demo account for testing
    )
    
    try:
        # Connect to Quotex
        if await bot.connect():
            
            # Test 1: Get balance
            balance = await bot.get_balance()
            print(f"💰 Balance: ${balance:.2f}")
            
            # Test 2: Get candle data
            candles = await bot.get_candles("EURUSD_otc", period=60, count=10)
            if candles:
                print(f"📊 Latest candle: {candles[-1]}")
            
            # Test 3: Place a small test trade
            test_signal = {
                'asset': 'EURUSD_otc',
                'direction': 'call',
                'amount': 1.0,  # Small amount for testing
                'duration': 60
            }
            
            result = await bot.process_signal(test_signal)
            print(f"🎯 Trade result: {result}")
            
        else:
            print("❌ Failed to connect to Quotex")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        
    finally:
        await bot.close()

if __name__ == "__main__":
    asyncio.run(test_trading_bot())
