<!DOCTYPE html><html lang="pt"><head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Login na Web da Quotex | Login do QxBroker</title>
            <link rel="canonical" href="https://qxbroker.com/pt/sign-in/">
        <link rel="apple-touch-icon" sizes="180x180" href="https://market-qx.pro/profile/favicons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://market-qx.pro/profile/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://market-qx.pro/profile/favicons/favicon-16x16.png">
    <link rel="shortcut icon" href="https://market-qx.pro/profile/favicons/favicon.ico">
    <link rel="mask-icon" href="https://market-qx.pro/profile/favicons/safari-pinned-tab.svg" color="#a0002a">
    <link rel="manifest" href="https://market-qx.pro/profile/favicons/site.webmanifest">
    <meta name="description" content="Login da Quotex. Faça login no gabinete pessoal da corretora Qx, aprenda em uma conta demo e comece a fazer investimentos reais. O depósito mínimo é de apenas US$ 10">
    <meta name="msapplication-TileColor" content="#a0002a">
    <meta name="msapplication-config" content="https://market-qx.pro/profile/favicons/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
    <meta name="interkassa-verification" content="52ae35204ccc1b97dfc591cb53ca5d3a">
    <link rel="shortcut icon" href="https://market-qx.pro/profile/images/favicon.png">

    <link rel="stylesheet" href="https://market-qx.pro/site/css/main.min.css?8">
            <link rel="stylesheet" href="https://market-qx.pro/css/debugbar/debug-bar-decorator.css">
        <script src="https://unpkg.com/imask"></script>
    <meta name="yandex-verification" content="62662e7489c4f276">
        <!-- Google tag (gtag.js) -->
<script async="" src="https://www.googletagmanager.com/gtag/js?id=G-L4T5GBPFHJ"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-L4T5GBPFHJ');
</script>    </head>
<body class="">
<bdi>
<!-- prettier-ignore -->
<div>
    <div class="main__platform-content">
        <header class="header" id="top">
            <div class="header__menu">
                <a class="header__logo" href="https://market-qx.pro/pt/"><i class="quotex-logo"></i></a>

                <nav class="header__links">
                    <ul class="header__list">
                        <li class="header__list--item">
                            <a href="https://market-qx.pro/pt/sign-up/fast/">
                                Conta demo                            </a>
                        </li>
                        <li class="header__list--item ">
                            <a href="https://market-qx.pro/pt/about-us/">
                                Sobre nós                            </a>
                        </li>
                        <li class="header__list--item ">
                            <a href="https://market-qx.pro/pt/faq/">FAQ</a>
                        </li>
                        <li class="header__list--item">
                            <a href="https://blog.qxbroker.com/pt/">
                                Blog                            </a>
                        </li>
                    </ul>
                </nav>

                <div class="header__buttons">
                    <a href="https://market-qx.pro/pt/sign-in/" class="header__button-log-in ">Conecte-se</a>
                    <a href="https://market-qx.pro/pt/sign-up/" class="header__button" id="button-sign-up">Inscrever-se</a>

                    <a class="header__language language ">
                        <svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M2.36666 8.20622C2.66248 5.57837 4.49411 3.41541 6.94277 2.63608C6.0345 4.6496 5.51928 6.42527 5.42018 8.20622H2.36666ZM6.92284 8.20622C7.03549 6.44654 7.61926 4.61824 8.74705 2.32818C8.81644 2.32603 8.88611 2.32495 8.95603 2.32495C8.98541 2.32495 9.01475 2.32514 9.04405 2.32552C10.1727 4.61682 10.7569 6.44586 10.8696 8.20622H6.92284ZM10.8525 9.70622H6.93991C7.08884 11.4396 7.68449 13.276 8.75925 15.5847C8.82461 15.5866 8.89021 15.5876 8.95603 15.5876C8.98139 15.5876 9.00672 15.5875 9.03201 15.5872C10.1075 13.2773 10.7035 11.4402 10.8525 9.70622ZM10.7933 15.3298C11.6865 13.2752 12.2242 11.4792 12.3573 9.70622H15.5454C15.2426 12.3967 13.3299 14.5999 10.7933 15.3298ZM12.3722 8.20622C12.2724 6.41274 11.7506 4.62462 10.8304 2.59356C13.3485 3.33417 15.244 5.52892 15.5454 8.20622H12.3722ZM2.36664 9.70622H5.43511C5.56728 11.4674 6.09869 13.2512 6.98125 15.2886C4.51327 14.5197 2.66398 12.3479 2.36664 9.70622ZM8.95603 0.824951C4.46523 0.824951 0.824707 4.46547 0.824707 8.95628C0.824707 13.4471 4.46523 17.0876 8.95603 17.0876C13.4468 17.0876 17.0874 13.4471 17.0874 8.95628C17.0874 4.46547 13.4468 0.824951 8.95603 0.824951Z"></path>
                        </svg>
                        <div class="header__language-text">pt</div>
                    </a>

                    <div id="countriesMenu" class="countries__menu ">
                                                    <a href="/ar/sign-in/">العربية</a>
                                                    <a href="/bn/sign-in/">বাংলা</a>
                                                    <a href="/en/sign-in/">English</a>
                                                    <a href="/es/sign-in/">Español</a>
                                                    <a href="/fa/sign-in/">فارسی</a>
                                                    <a href="/fl/sign-in/">Filipino</a>
                                                    <a href="/fr/sign-in/">Français</a>
                                                    <a href="/ha/sign-in/">Hausa</a>
                                                    <a href="/hi/sign-in/">हिन्दी</a>
                                                    <a href="/id/sign-in/">Indonesian</a>
                                                    <a href="/ja/sign-in/">日本語</a>
                                                    <a href="/ko/sign-in/">한국어</a>
                                                    <a href="/ms/sign-in/">Malay</a>
                                                    <a href="/pt/sign-in/">Português</a>
                                                    <a href="/ru/sign-in/">Русский</a>
                                                    <a href="/sw/sign-in/">Kiswahili</a>
                                                    <a href="/th/sign-in/">ไทย</a>
                                                    <a href="/tr/sign-in/">Türkçe</a>
                                                    <a href="/ua/sign-in/">Українська</a>
                                                    <a href="/uz/sign-in/">Oʻzbekcha</a>
                                                    <a href="/vt/sign-in/">Tiếng Việt</a>
                                                    <a href="/zh/sign-in/">中文</a>
                                            </div>
                </div>

                <button id="mobile-trigger" class="header__mobile-button">
                    <div id="nav-icon">
                        <span></span>
                        <span></span>
                    </div>
                </button>

                <div class="header__mobile-container">
                    <div class="header__mobile-block">
                        <div class="header__mobile-container__nav">
                            <a href="https://market-qx.pro/pt/demo-trade/">Conta demo</a>
                        </div>
                        <div class="header__mobile-container__nav">
                            <a href="https://market-qx.pro/pt/about-us/">Sobre nós</a>
                        </div>
                        <div class="header__mobile-container__nav">
                            <a href="https://market-qx.pro/pt/faq/">FAQ</a>
                        </div>
                        <div class="header__mobile-container__nav">
                            <a href="https://blog.qxbroker.com/pt/">Blog</a>
                        </div>
                    </div>

                    <div class="header__mobile-container__footer-buttons">
                        <a href="https://market-qx.pro/pt/sign-in/" class="header__mobile-container__footer-log-in">Entrar</a>
                        <a href="https://market-qx.pro/pt/sign-up/" class="header__mobile-container__footer-sign-up">Inscrever-se</a>
                    </div>

                    <div class="header__mobile-container__footer">
                        <div>
                            <div id="countriesMenu-mobile" class="countries__menu mobile">
                                                                    <a href="/ar/sign-in/">العربية</a>
                                                                    <a href="/bn/sign-in/">বাংলা</a>
                                                                    <a href="/en/sign-in/">English</a>
                                                                    <a href="/es/sign-in/">Español</a>
                                                                    <a href="/fa/sign-in/">فارسی</a>
                                                                    <a href="/fl/sign-in/">Filipino</a>
                                                                    <a href="/fr/sign-in/">Français</a>
                                                                    <a href="/ha/sign-in/">Hausa</a>
                                                                    <a href="/hi/sign-in/">हिन्दी</a>
                                                                    <a href="/id/sign-in/">Indonesian</a>
                                                                    <a href="/ja/sign-in/">日本語</a>
                                                                    <a href="/ko/sign-in/">한국어</a>
                                                                    <a href="/ms/sign-in/">Malay</a>
                                                                    <a href="/pt/sign-in/">Português</a>
                                                                    <a href="/ru/sign-in/">Русский</a>
                                                                    <a href="/sw/sign-in/">Kiswahili</a>
                                                                    <a href="/th/sign-in/">ไทย</a>
                                                                    <a href="/tr/sign-in/">Türkçe</a>
                                                                    <a href="/ua/sign-in/">Українська</a>
                                                                    <a href="/uz/sign-in/">Oʻzbekcha</a>
                                                                    <a href="/vt/sign-in/">Tiếng Việt</a>
                                                                    <a href="/zh/sign-in/">中文</a>
                                                            </div>

                            <div class="header__mobile-container__footer-language language-mobile">
                                <div class="header__mobile-container__footer-language__flag">
                                    <svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M2.36666 8.20622C2.66248 5.57837 4.49411 3.41541 6.94277 2.63608C6.0345 4.6496 5.51928 6.42527 5.42018 8.20622H2.36666ZM6.92284 8.20622C7.03549 6.44654 7.61926 4.61824 8.74705 2.32818C8.81644 2.32603 8.88611 2.32495 8.95603 2.32495C8.98541 2.32495 9.01475 2.32514 9.04405 2.32552C10.1727 4.61682 10.7569 6.44586 10.8696 8.20622H6.92284ZM10.8525 9.70622H6.93991C7.08884 11.4396 7.68449 13.276 8.75925 15.5847C8.82461 15.5866 8.89021 15.5876 8.95603 15.5876C8.98139 15.5876 9.00672 15.5875 9.03201 15.5872C10.1075 13.2773 10.7035 11.4402 10.8525 9.70622ZM10.7933 15.3298C11.6865 13.2752 12.2242 11.4792 12.3573 9.70622H15.5454C15.2426 12.3967 13.3299 14.5999 10.7933 15.3298ZM12.3722 8.20622C12.2724 6.41274 11.7506 4.62462 10.8304 2.59356C13.3485 3.33417 15.244 5.52892 15.5454 8.20622H12.3722ZM2.36664 9.70622H5.43511C5.56728 11.4674 6.09869 13.2512 6.98125 15.2886C4.51327 14.5197 2.66398 12.3479 2.36664 9.70622ZM8.95603 0.824951C4.46523 0.824951 0.824707 4.46547 0.824707 8.95628C0.824707 13.4471 4.46523 17.0876 8.95603 17.0876C13.4468 17.0876 17.0874 13.4471 17.0874 8.95628C17.0874 4.46547 13.4468 0.824951 8.95603 0.824951Z"></path>
                                    </svg>
                                </div>
                                pt                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <div class="main__platform-video__none">
            <div class="main__platform-video">
                <video class="main__platform-video__media" src="https://market-qx.pro/site/img/main_bg.mp4" autoplay="" playsinline="" muted="" loop=""></video>
            </div>
        </div>
<div class="main sign__container">
    <div class="modal-sign__container sign__form">
                    <div class="sign__title">Faça Login Em Sua Conta</div>
        
        <div class="modal-sign__block">
            <div class="modal-sign__tabs form" dir="auto">
                <div class="modal-sign__tabs-block">
                    <a href="https://market-qx.pro/pt/sign-in/" class="modal-sign__tab active" data-value="1">
                        Entrar                    </a>
                    <a href="https://market-qx.pro/pt/sign-up/" class="modal-sign__tab " data-value="2">
                        Cadastro                    </a>
                </div>
            </div>

                        <div id="tab-2" class="tabs__item ">
                                                                    <form action="https://market-qx.pro/pt/sign-up/" method="post" onsubmit="this.elements['time_offset'].value = -new Date().getTimezoneOffset() * 60; return QXvId.submit(this, '__alreadySubmited'); " dir="auto">
    <input type="hidden" name="_token" value="EUnrMfcHUFqqmJ51JiSKVgH210tPOcgd2sFlHBqS" style="">    <input type="hidden" name="time_offset" style="">

    
    <div id="select-country-name" class="select-input-container">
        <input id="input-country-name" type="text" placeholder="Procurar" class="select-input" style="">
        <input id="input-clear-button" class="select-button-close" type="button" value="X" style="">
        <div class="modal-sign__input select select-icon">
            <div class="icon-input">
                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                  <g opacity="0.3" clip-path="url(#clip0_7761_804)">
                    <path d="M8.08594 0.34375C3.86406 0.34375 0.429688 3.77812 0.429688 8C0.429688 12.2219 3.86406 15.6562 8.08594 15.6562C12.3078 15.6562 15.7422 12.2219 15.7422 8C15.7422 3.77812 12.3078 0.34375 8.08594 0.34375ZM14.4328 7.3625H11.3422C11.2766 5.3125 10.8828 3.30937 10.1797 1.97187C12.4797 2.775 14.1859 4.85625 14.4328 7.3625ZM8.08594 14.3813C7.31719 14.3813 6.22344 12.1656 6.10156 8.6375H10.0672C9.94844 12.1625 8.85469 14.3813 8.08594 14.3813ZM6.10156 7.3625C6.22031 3.8375 7.31719 1.61875 8.08594 1.61875C8.85469 1.61875 9.94844 3.83438 10.0703 7.3625H6.10156ZM5.99219 1.97187C5.28906 3.30937 4.89531 5.30937 4.82656 7.35938H1.73906C1.98594 4.85625 3.69219 2.775 5.99219 1.97187ZM1.73906 8.6375H4.82969C4.89844 10.6875 5.29219 12.6906 5.99531 14.025C3.69219 13.225 1.98594 11.1437 1.73906 8.6375ZM10.1797 14.0281C10.8828 12.6906 11.2766 10.6906 11.3453 8.64062H14.4359C14.1859 11.1438 12.4797 13.225 10.1797 14.0281Z" fill="white"></path>
                  </g>
                  <defs>
                    <clipPath id="clip0_7761_804">
                      <rect width="16" height="16" fill="white" transform="translate(0.0859375)"></rect>
                    </clipPath>
                  </defs>
                </svg>
            </div>

            <label class="modal-sign__input-label ">País / Região de residência</label>
            <select name="country-code" class="select__element">
                            </select>
            <div class="modal-sign__input-select form__select select__wrapper "><div class="select__result"><div></div><i class="select__icon"></i></div><div class="select__options"></div></div>
                    </div>
    </div>

    <div class="modal-sign__input select">
        <label class="modal-sign__input-label ">Moeda</label>
        <select name="currency" class="select__element">
                            <option value="EUR">EUR</option>
                            <option value="GBP">GBP</option>
                            <option value="USD" selected="">USD</option>
                            <option value="BRL">BRL</option>
                            <option value="IDR">IDR</option>
                            <option value="MYR">MYR</option>
                            <option value="INR">INR</option>
                            <option value="KZT">KZT</option>
                            <option value="RUB">RUB</option>
                            <option value="THB">THB</option>
                            <option value="UAH">UAH</option>
                            <option value="VND">VND</option>
                            <option value="NGN">NGN</option>
                            <option value="EGP">EGP</option>
                            <option value="MXN">MXN</option>
                            <option value="JPY">JPY</option>
                            <option value="BDT">BDT</option>
                            <option value="PKR">PKR</option>
                            <option value="PHP">PHP</option>
                            <option value="TRY">TRY</option>
                            <option value="KRW">KRW</option>
                    </select>
        <div class="modal-sign__input-select form__select select__wrapper "><div class="select__result"><div><div data-value="USD"><span class="select__text">USD</span></div></div><i class="select__icon"></i></div><div class="select__options"><div data-value="EUR"><span class="select__text">EUR</span></div><div data-value="GBP"><span class="select__text">GBP</span></div><div data-value="USD"><span class="select__text">USD</span></div><div data-value="BRL"><span class="select__text">BRL</span></div><div data-value="IDR"><span class="select__text">IDR</span></div><div data-value="MYR"><span class="select__text">MYR</span></div><div data-value="INR"><span class="select__text">INR</span></div><div data-value="KZT"><span class="select__text">KZT</span></div><div data-value="RUB"><span class="select__text">RUB</span></div><div data-value="THB"><span class="select__text">THB</span></div><div data-value="UAH"><span class="select__text">UAH</span></div><div data-value="VND"><span class="select__text">VND</span></div><div data-value="NGN"><span class="select__text">NGN</span></div><div data-value="EGP"><span class="select__text">EGP</span></div><div data-value="MXN"><span class="select__text">MXN</span></div><div data-value="JPY"><span class="select__text">JPY</span></div><div data-value="BDT"><span class="select__text">BDT</span></div><div data-value="PKR"><span class="select__text">PKR</span></div><div data-value="PHP"><span class="select__text">PHP</span></div><div data-value="TRY"><span class="select__text">TRY</span></div><div data-value="KRW"><span class="select__text">KRW</span></div></div></div>
            </div>

   <div class="modal-sign__input">
        <label class="modal-sign__input-label ">E-mail</label>
        <input type="email" name="email" value="" class="modal-sign__input-value focus-tooltip" required="1" autocomplete="off" id="emailInput" style="">        <div class="modal-sign__input-tooltip">Não é possível alterar o e-mail no futuro. Ao especificar um e-mail inexistente ou incorreto, a retirada de fundos da conta será impossível.</div>
               <div data-role="email-misspell-hint" class="misspell-hint hide hint -danger">
           Você quis dizer:           <span data-role="email-misspell-suggestion" class="misspell-hint-suggestion"></span>
       </div>
       <div id="emailSuggestions" class="modal-sign__email-dropdown" style="display: none;">
         <ul></ul>
       </div>
    </div>
    <div class="modal-sign__input">
        <label class="modal-sign__input-label ">Senha</label>
        <input type="password" name="password" value="" class="modal-sign__input-value" required="1" autocomplete="off" style="">            </div>

    <div class="modal-sign__input">
        <div class="modal-sign__block-checked">
            <label class="modal-sign__checked-container">
                <input type="checkbox" name="rules" value="1" style="">                <span class="modal-sign__checked-checkmark"></span>
                <div>
                    Confirmo que tenho 18 anos ou mais e aceito o <a href="https://market-qx.pro/documents/pt/Service_Agreement_QTX.pdf" target="_blank" class="modal-sign__block-checked-forgot">Acordo de Serviço</a>                </div>
            </label>
        </div>
            </div>

    <div class="modal-sign__input">
        <div class="modal-sign__block-checked">
            <label class="modal-sign__checked-container">
                <input type="checkbox" name="not-us-citizen" value="1" style="">                <span class="modal-sign__checked-checkmark"></span>
                <div>
                    Declaro e confirmo que não sou cidadão ou residente dos EUA para fins fiscais                </div>
            </label>
        </div>
            </div>

    
    <button class="modal-sign__block-button ">
        <div>Cadastro</div>
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle opacity="0.3" cx="12" cy="12.75" r="12" transform="rotate(-90 12 12.75)" fill="white"></circle>
            <path d="M12.5497 7.95321L12.1565 8.34628C12.0329 8.4697 11.9645 8.63467 11.9645 8.81028C11.9645 8.98609 12.0329 9.16599 12.1565 9.2894L14.6827 11.8308H6.65668C6.29473 11.8308 6 12.095 6 12.4568V13.013C6 13.3748 6.29473 13.7058 6.65668 13.7058H14.7113L12.1565 16.2421C12.0329 16.3657 11.9645 16.5212 11.9645 16.697C11.9645 16.8726 12.0329 17.033 12.1565 17.1565L12.5497 17.5475C12.8056 17.8033 13.2219 17.8022 13.4778 17.5464L17.8084 13.2153C17.9317 13.0919 18 12.9265 18 12.7494V12.7475C18 12.5718 17.9317 12.4068 17.8084 12.2835L13.4779 7.95321C13.222 7.69721 12.8056 7.69721 12.5497 7.95321Z" fill="white"></path>
        </svg>
    </button>
</form>

<script>
    document.addEventListener("DOMContentLoaded", () => {
         new EmailMisspellChecker({
             emailInput: document.querySelector('#tab-2 input[name=email]'),
             hintNode: document.querySelector('[data-role=email-misspell-hint]')
         });
    });
</script>

<script type="text/javascript">
    window.tJSON = {"No matches.":"Sem correspondência"};
</script>                    <div class="modal-sign__footer">
                        <div class="modal-sign__footer-header">
                            <div class="modal-sign__footer-header-text">Entrar com</div>
                        </div>

                        <div class="modal-sign__social">
                            <a href="https://market-qx.pro/oauth/facebook" class="modal-sign__social-button facebook ">
                                <i class="icon icon__facebook-login"></i>
                            </a>
                            <a href="https://market-qx.pro/oauth/google" class="modal-sign__social-button google">
                                <i class="icon icon__google"></i>
                            </a>
                        </div>
                    </div>
                            </div>
            <div id="tab-1" class="tabs__item active">
                                    <form action="https://market-qx.pro/pt/sign-in/" method="post" onsubmit="return QXvId.submit(this);" dir="auto">
    <input type="hidden" name="_token" value="EUnrMfcHUFqqmJ51JiSKVgH210tPOcgd2sFlHBqS" style="">
    
   <div class="modal-sign__input">
        <label class="modal-sign__input-label ">E-mail</label>
        <input type="email" name="email" value="" class="modal-sign__input-value" required="1" style="">        <div class="modal-sign__input-tooltip">Não é possível alterar o e-mail no futuro. Ao especificar um e-mail inexistente ou incorreto, a retirada de fundos da conta será impossível.</div>
            </div>

    <div class="modal-sign__input">
        <label class="modal-sign__input-label ">Senha</label>
        <input type="password" name="password" value="" class="modal-sign__input-value" required="1" autocomplete="off" style="">            </div>

    <div class="modal-sign__input">
        <div class="modal-sign__block-checked">
            <label class="modal-sign__checked-container">
                <input type="checkbox" name="remember" value="1" checked="checked" style="">                <span class="modal-sign__checked-checkmark"></span>
                Lembrar-me            </label>
            <a href="https://market-qx.pro/pt/password-recovery/" class="modal-sign__block-checked-forgot">
                Esqueceu sua senha?            </a>
        </div>
    </div>


    
    <button class="modal-sign__block-button ">
        <div>Entrar</div>
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle opacity="0.3" cx="12" cy="12.75" r="12" transform="rotate(-90 12 12.75)" fill="white"></circle>
            <path d="M12.5497 7.95321L12.1565 8.34628C12.0329 8.4697 11.9645 8.63467 11.9645 8.81028C11.9645 8.98609 12.0329 9.16599 12.1565 9.2894L14.6827 11.8308H6.65668C6.29473 11.8308 6 12.095 6 12.4568V13.013C6 13.3748 6.29473 13.7058 6.65668 13.7058H14.7113L12.1565 16.2421C12.0329 16.3657 11.9645 16.5212 11.9645 16.697C11.9645 16.8726 12.0329 17.033 12.1565 17.1565L12.5497 17.5475C12.8056 17.8033 13.2219 17.8022 13.4778 17.5464L17.8084 13.2153C17.9317 13.0919 18 12.9265 18 12.7494V12.7475C18 12.5718 17.9317 12.4068 17.8084 12.2835L13.4779 7.95321C13.222 7.69721 12.8056 7.69721 12.5497 7.95321Z" fill="white"></path>
        </svg>
    </button>
</form>
                    <div class="modal-sign__footer">
                        <div class="modal-sign__footer-header">
                            <div class="modal-sign__footer-header-text">Entrar com</div>
                        </div>

                        <div class="modal-sign__social">
                            <a href="https://market-qx.pro/oauth/facebook" class="modal-sign__social-button facebook ">
                                <i class="icon icon__facebook-login"></i>
                            </a>
                            <a href="https://market-qx.pro/oauth/google" class="modal-sign__social-button google">
                                <i class="icon icon__google"></i>
                            </a>
                        </div>
                    </div>
                            </div>
            <div class="popup-modal__footer"></div>
        </div>
    </div>
</div>
<!-- prettier-ignore -->
<footer class="footer" dir="auto">
    <div class="footer__block">
        <a class="footer__logo" href="#">
            <img src="https://market-qx.pro/site/img/quotex_logo-white.svg" alt="quotex">
        </a>

        <div class="footer__links">
            <nav>
                <ul>
                    <li>
                        <a class="footer__links-section " href="https://market-qx.pro/pt/faq/" target="_blank">
                            Perguntas frequentes
                            <svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.52432 5.20379C6.26968 4.92877 6.26968 4.49246 6.52432 4.21744C6.79664 3.92332 7.24999 3.92915 7.5079 4.22539L11.4757 8.51083C11.5998 8.64491 11.6667 8.82538 11.6667 9.00401C11.6667 9.18867 11.6008 9.36204 11.4757 9.49718L7.5079 13.7826C7.23955 14.0725 6.79268 14.0725 6.52432 13.7826C6.26968 13.5076 6.26968 13.0713 6.52432 12.7963L10.0426 8.99631L6.52432 5.20379Z"></path>
                            </svg>
                        </a>
                    </li>

                    <li>
                        <a class="footer__links-item" href="https://market-qx.pro/pt/faq/">
                            Questões gerais                        </a>
                    </li>
                    <li>
                        <a class="footer__links-item" href="https://market-qx.pro/pt/faq/#financial">
                            Questões financeiras                        </a>
                    </li>
                    <li>
                        <a class="footer__links-item" href="https://market-qx.pro/pt/faq/#verification">
                            Verificação                        </a>
                    </li>
                </ul>
            </nav>

            <nav>
                <ul>
                    <li>
                        <a class="footer__links-section " href="https://market-qx.pro/pt/about-us/" target="_blank">
                            Sobre nós
                            <svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.52432 5.20379C6.26968 4.92877 6.26968 4.49246 6.52432 4.21744C6.79664 3.92332 7.24999 3.92915 7.5079 4.22539L11.4757 8.51083C11.5998 8.64491 11.6667 8.82538 11.6667 9.00401C11.6667 9.18867 11.6008 9.36204 11.4757 9.49718L7.5079 13.7826C7.23955 14.0725 6.79268 14.0725 6.52432 13.7826C6.26968 13.5076 6.26968 13.0713 6.52432 12.7963L10.0426 8.99631L6.52432 5.20379Z"></path>
                            </svg>
                        </a>
                    </li>

                    <li>
                        <a class="footer__links-item" href="https://market-qx.pro/pt/contacts/">Contatos</a>
                    </li>
                </ul>
            </nav>

            <nav>
                <ul>
                    <li>
                        <div class="footer__links-section__title">
                            Mais                        </div>
                    </li>

                    <li>
                        <a class="footer__links-item" href="https://market-qx.pro/pt/sign-up/fast/">Conta demo</a>
                    </li>
                    <li>
                        <a class="footer__links-item" href="https://quotex-partner.com/" target="_blank">
                            Programa de afiliados                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M11.1667 5C11.1667 4.72386 11.3905 4.5 11.6667 4.5H15C15.2761 4.5 15.5 4.72386 15.5 5V8.33333C15.5 8.60948 15.2761 8.83333 15 8.83333C14.7239 8.83333 14.5 8.60948 14.5 8.33333V6.20711L10.6869 10.0202C10.4916 10.2155 10.175 10.2155 9.97978 10.0202C9.78452 9.82496 9.78452 9.50838 9.97978 9.31311L13.7929 5.5H11.6667C11.3905 5.5 11.1667 5.27614 11.1667 5Z" fill="#8D94AB"></path>
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M4.5 7.10001C4.5 6.05067 5.35066 5.20001 6.4 5.20001H8.4C8.67614 5.20001 8.9 5.42387 8.9 5.70001C8.9 5.97615 8.67614 6.20001 8.4 6.20001H6.4C5.90294 6.20001 5.5 6.60296 5.5 7.10001V13.6C5.5 14.0971 5.90294 14.5 6.4 14.5H12.9C13.3971 14.5 13.8 14.0971 13.8 13.6V11.6C13.8 11.3239 14.0239 11.1 14.3 11.1C14.5761 11.1 14.8 11.3239 14.8 11.6V13.6C14.8 14.6493 13.9493 15.5 12.9 15.5H6.4C5.35066 15.5 4.5 14.6493 4.5 13.6V7.10001Z" fill="#8D94AB"></path>
                            </svg>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <div>
            <div class="footer__social-title">Descarregar a aplicação</div>

            <a class="footer__playmarket" href="https://play.google.com/store/apps/details?id=io.quotex.x">
                <img src="https://market-qx.pro/site/img/playmarket.svg" alt="playmarket">
            </a>

            <div class="footer__social-title">Siga-nos nas redes sociais</div>

            <a class="footer__social-item" href="https://www.facebook.com/quotexio/" target="_blank">
                <i class="icon icon__facebook"></i>
                <div>10K+</div>
            </a>

            <a class="footer__social-item" href="https://www.instagram.com/quotex_io/" target="_blank">
                <i class="icon icon__instagram"></i>
                <div>76K+</div>
            </a>

            <a class="footer__social-item" href="https://t.me/quotex_pt" target="_blank">
                <i class="icon icon__telegram"></i>
                <div>90K+</div>
            </a>
        </div>
    </div>

    <div class="footer__container">
        <nav>
            <ul>
                <li><p class="footer__section-title">Regulamentos</p></li>

                <li>
                    <a class="footer__links-item" href="https://market-qx.pro/documents/pt/Privacy_Policy_QTX.pdf" target="_blank">
                        Política de privacidade                    </a>
                </li>
                <li>
                    <a class="footer__links-item" href="https://market-qx.pro/documents/pt/Service_Agreement_QTX.pdf" target="_blank">
                        Acordo de serviço                    </a>
                </li>
                <li>
                    <a class="footer__links-item" href="https://market-qx.pro/documents/pt/Risk_Disclosure_QTX.pdf" target="_blank">
                        Advertência de risco                    </a>
                </li>
                <li>
                    <a class="footer__links-item" href="https://market-qx.pro/documents/pt/Rules_of_Trading_operations_QTX.pdf" target="_blank">
                        Regras de operações comerciais                    </a>
                </li>
                <li>
                    <a class="footer__links-item" href="https://market-qx.pro/documents/pt/Non-Trading_Operations_Regulations_QTX.pdf" target="_blank">
                        Regulamentos de operações não-comerciais                    </a>
                </li>
                <li>
                    <a class="footer__links-item" href="https://market-qx.pro/documents/pt/PaymentPolicyQTX.pdf" target="_blank">
                        Política de pagamento                    </a>
                </li>
            </ul>
        </nav>

        <div class="footer__text">
            <p>
                GRUPO ON SPOT LLC. Endereço: Main Street, P.O. Box 625, Charlestown, São Cristóvão e Névis.            </p>
            <br>
            <p>
                Os serviços do site não estão disponíveis em vários países, incluindo EUA, Canadá, Hong Kong, países do EEE, Israel e Rússia, bem como para pessoas com menos de 18 anos de idade.            </p>
            <br>
            <p>
                Aviso de risco: a negociação de Forex e instrumentos financeiros alavancados envolve risco significativo e pode resultar na perda de seu capital investido. Você não deve investir mais do que pode perder, e deve certificar-se de que compreende totalmente os riscos envolvidos. A negociação de produtos alavancados pode não ser adequada para todos os investidores. A negociação de produtos não alavancados, como ações, também envolve risco, pois o valor de uma ação pode tanto cair quanto subir, o que pode resultar em um rendimento menor do que o investido originalmente. O desempenho passado não é garantia de resultados futuros. Antes de negociar, leve em consideração seu nível de experiência e objetivos de investimento e procure aconselhamento financeiro independente, se necessário. É responsabilidade do cliente verificar se tem permissão para utilizar os serviços da marca Quotex de acordo com os requisitos legais de seu país de residência.            </p>
            <br><br>
            <p>ON SPOT LLC GROUP é o proprietário do domínio market-qx.pro.</p>
            <p>Copyright © 2025 Quotex. Todos os direitos reservados</p>
        </div>
    </div>
</footer>

<!-- prettier-ignore -->

<script type="text/javascript">
    window.settings = {"fpjsDomain":null,"csrf":"EUnrMfcHUFqqmJ51JiSKVgH210tPOcgd2sFlHBqS"};
</script>
<div class="modal js-account-closed-modal">
    <div class="modal-account-closed modal__dialog">
        <div>
            <button class="modal__close" id="close-button-terms-conditions">
                <svg data-testid="Icon" class="icon-close">
                    <use xlink:href="https://market-qx.pro/profile/images/spritemap.svg#icon-close"></use>
                </svg>
            </button>

            <div class="modal-account-closed__header">
                <svg>
                    <use xlink:href="https://market-qx.pro/profile/images/spritemap.svg#icon-information"></use>
                </svg>
                <div>Conta encerrada - por quê e o que fazer?</div>
            </div>

            <div class="modal-account-closed__body">
                <span>Existem várias razões pelas quais uma conta pode ser encerrada:</span>
                <br><br>
                <b>1. Nenhuma atividade.</b><br>
                <span>O motivo mais comum é que a conta foi fechada por estar inativa (sem logins/atividade) por um longo período de tempo – de 3 meses ou mais. Essas contas são excluídas se não houver fundos no saldo e não podem ser restauradas. Você é livre para registrar uma nova conta. (desde que não haja outras contas ativas registradas por você na Plataforma)</span>
                <br><br>
                <span><span class="modal-account-closed__prompt">*&nbsp;</span>O e-mail não pode ser reutilizado. Você precisaria usar um endereço de e-mail diferente.</span>
                <br><br>

                <b>2. Excluído pelo proprietário.</b><br>
                <span> Se não houver fundos no saldo, essas contas não podem ser restauradas. Como no caso anterior, você pode simplesmente certificar-se de que não há outras contas ativas registradas por você na Plataforma e criar uma nova.)</span>
                <br><br>
                <span><span class="modal-account-closed__prompt">*&nbsp;</span>Se você excluiu sua conta por engano e há fundos em seu saldo - entre em contato com o suporte para obter detalhes (usando o formulário «Contatos» na página principal do site). Os operadores irão verificar e ver se a conta pode ser restaurada.</span>
                <br><br>

                <div class="modal-account-closed__body-block">
                    <div class="modal-account-closed__body-accordion">
                        Se for possível restaurar a conta, você será solicitado a fornecer
                        <div class="modal-account-closed__body-plus">
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.84615 0H7.15385V4.84615H12V7.15385H7.15385V12H4.84615V7.15385H0V4.84615H4.84615V0Z" fill="#2B99FF"></path>
                            </svg>
                        </div>

                        <div class="modal-account-closed__body-minus">
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.15385 4.84766H12V7.15535H7.15385H4.84615H0V4.84766H4.84615H7.15385Z" fill="#2B99FF"></path>
                            </svg>
                        </div>
                    </div>

                    <div class="modal-account-closed__body-panel">
                        <ul>
                            <li>Uma foto sua em alta resolução (selfie) na qual você está segurando seu documento de identificação (seu passaporte ou documento de identidade servirá) junto com uma folha de papel com o nome «QUOTEX» escrito à mão, data atual e seu assinatura. Seu rosto, corpo e ambos os braços devem estar visíveis. Os detalhes do documento devem ser claros e legíveis.</li>
                            <li>Capturas de tela dos recibos de depósitos nessa conta (um extrato bancário ou recibos detalhados do sistema de pagamento que você usou para depositar servirão).</li>
                        </ul>
                    </div>
                </div>

                <br>
                <b>3. Contas duplicadas.</b><br>
                <span>Só é permitido ter uma conta ativa na Plataforma. Se forem detectadas outras contas registradas pela mesma pessoa, elas poderão ser excluídas sem aviso prévio (c 1.30 do Contrato de Serviço).</span>
                <br><br>

                <b>4. Excluído por violação do Contrato de Serviço.</b><br>
                <span>O proprietário é notificado sobre os detalhes da violação e a possibilidade de reembolso e, se aplicável, é solicitado a fornecer os documentos necessários.)</span>
                <br><br>
                <span><span class="modal-account-closed__prompt">*&nbsp;</span>Em caso de detecção automática de violações (por exemplo, usando software de negociação automatizado) - a Empresa reserva-se o direito de não notificar o proprietário com antecedência. (Você pode entrar em contato com o suporte através do formulário "Contatos" na parte inferior da página inicial do site para obter detalhes e reembolso (se aplicável). Lembramos que todos os documentos estatutários (Acordo de Serviço e seus anexos) estão disponíveis publicamente e podem ser revistos em a qualquer momento no site da Empresa.</span>
            </div>
        </div>
    </div>
</div>
<script src="https://market-qx.pro/site/js/main.min.js?7"></script>
<a href="//freekassa.ru/" style="display: none"><img src="//www.free-kassa.ru/img/fk_btn/17.png" title="Приём оплаты на сайте картами"></a>


</div></div></bdi></body></html>