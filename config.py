#!/usr/bin/env python3
"""
Trading Bot Configuration
"""

# Oanda API Configuration
OANDA_CONFIG = {
    "ACCESS_TOKEN": "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1",
    "ACCOUNT_ID": "101-004-********-001",
    "BASE_URL": "https://api-fxpractice.oanda.com",  # Practice environment
    "ENVIRONMENT": "practice"  # practice or live
}

# Currency pairs to monitor - EXPANDED LIST
CURRENCY_PAIRS = [
    # Major Pairs
    "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD", "USD_CHF",

    # Cross Pairs
    "EUR_GBP", "EUR_JPY", "EUR_CHF", "EUR_AUD", "EUR_CAD", "EUR_NZD",
    "GBP_JPY", "GBP_CHF", "GBP_AUD", "GBP_CAD", "GBP_NZD",
    "AUD_JPY", "AUD_CHF", "AUD_CAD", "AUD_NZD",
    "CAD_JPY", "CAD_CHF", "CHF_JPY",
    "NZD_USD", "NZD_JPY", "NZD_CHF", "NZD_CAD",

    # Commodity Pairs
    "USD_NOK", "USD_SEK", "EUR_NOK", "EUR_SEK",

    # Additional Popular Pairs
    "USD_ZAR", "USD_MXN", "USD_SGD", "USD_HKD"
]

# Trading Configuration
TRADING_CONFIG = {
    "CANDLE_INTERVAL": "M1",  # 1-minute candles (default)
    "FETCH_INTERVAL": 58,     # Fetch data 2 seconds before next candle (58 seconds)
    "LOOKBACK_CANDLES": 100,  # Number of historical candles to fetch for analysis
    "MIN_CONFIDENCE": 0.6,    # Minimum confidence threshold for signals
}

# Timeframe Configuration for Rule-Based Trading
TIMEFRAME_CONFIG = {
    "1min": {
        "granularity": "M1",
        "interval_seconds": 60,
        "fetch_before_seconds": 2,
        "display_name": "1 Minute",
        "description": "Signals every 1 minute"
    },
    "2min": {
        "granularity": "M2",
        "interval_seconds": 120,
        "fetch_before_seconds": 2,
        "display_name": "2 Minutes",
        "description": "Signals every 2 minutes"
    },
    "5min": {
        "granularity": "M5",
        "interval_seconds": 300,
        "fetch_before_seconds": 2,
        "display_name": "5 Minutes",
        "description": "Signals every 5 minutes"
    },
    "10min": {
        "granularity": "M10",
        "interval_seconds": 600,
        "fetch_before_seconds": 2,
        "display_name": "10 Minutes",
        "description": "Signals every 10 minutes"
    },
    "15min": {
        "granularity": "M15",
        "interval_seconds": 900,
        "fetch_before_seconds": 2,
        "display_name": "15 Minutes",
        "description": "Signals every 15 minutes"
    },
    "30min": {
        "granularity": "M30",
        "interval_seconds": 1800,
        "fetch_before_seconds": 2,
        "display_name": "30 Minutes",
        "description": "Signals every 30 minutes"
    },
    "1hour": {
        "granularity": "H1",
        "interval_seconds": 3600,
        "fetch_before_seconds": 2,
        "display_name": "1 Hour",
        "description": "Signals every 1 hour"
    }
}

# Strategy Configuration
STRATEGY_CONFIG = {
    "S1": {
        "name": "Breakout with Volume",
        "description": "Volume Breakout, Support/Resistance Levels - Best for volatile markets",
        "accuracy": "70-80%",
        "file": "output_signals_S1.csv",
        "signal_column": "strategy1_signal",
        "enabled": True
    },
    "S2": {
        "name": "Pullback Entry Strategy",
        "description": "EMA 20 Pullback, RSI(14), Volume Confirmation - High accuracy trend continuation",
        "accuracy": "88%",
        "file": "output_signals_S2.csv",
        "signal_column": "strategy2_signal",
        "enabled": True
    },
    "S3": {
        "name": "Support/Resistance Rejection",
        "description": "Support/Resistance Levels, Wick Analysis - Best for range-bound markets",
        "accuracy": "70-80%",
        "file": "output_signals_S3.csv",
        "signal_column": "strategy3_signal",
        "enabled": True
    },
    "S4": {
        "name": "Trendline Break with Rejection",
        "description": "Trendline Analysis, Wick Patterns - Best for trend reversal detection",
        "accuracy": "65-75%",
        "file": "output_signals_S4.csv",
        "signal_column": "strategy4_signal",
        "enabled": True
    },
    "S5": {
        "name": "Pre-Candle Momentum Strategy",
        "description": "EMA 5/9, RSI(14), Volume Spike - Best for trending markets (ADX > 25)",
        "accuracy": "75-85%",
        "enabled": True
    },
    "S6": {
        "name": "Pre-Candle Reversal Strategy",
        "description": "Bollinger Bands, Stochastic, Support/Resistance - Best for ranging markets (ADX < 25)",
        "accuracy": "80-90%",
        "enabled": True
    },
    "S7": {
        "name": "Pre-Candle Breakout Strategy",
        "description": "Previous Candle High/Low, Volume Surge, MACD Cross - Best for high volatility",
        "accuracy": "75-85%",
        "enabled": True
    },
    "S8": {
        "name": "Pre-Candle Fakeout Strategy",
        "description": "Fibonacci Retracement, RSI Divergence - Best for false breakout traps",
        "accuracy": "85%+",
        "enabled": True
    },
    "S9": {
        "name": "Ranging Market Strategy",
        "description": "Support/Resistance + RSI(14) + Price Action - Best for sideways markets",
        "accuracy": "75-85%",
        "enabled": True
    },
    "S10": {
        "name": "Trending Market Strategy",
        "description": "EMA 20/50 + MACD + Pullback Confirmation - Best for trending markets",
        "accuracy": "78-88%",
        "enabled": True
    }
}



# Display Configuration
DISPLAY_CONFIG = {
    "COLORS": {
        "BUY": "\033[92m",      # Green
        "SELL": "\033[91m",     # Red
        "HOLD": "\033[93m",     # Yellow
        "INFO": "\033[94m",     # Blue
        "SUCCESS": "\033[92m",  # Green
        "WARNING": "\033[93m",  # Yellow
        "ERROR": "\033[91m",    # Red
        "RESET": "\033[0m",     # Reset
        "BOLD": "\033[1m",      # Bold
        "HEADER": "\033[96m",   # Sky Blue (changed from Magenta)
        "DATE": "\033[96m",     # Cyan
        "TIME": "\033[97m",     # White
        "PAIR": "\033[92m",     # Green (changed from Blue)
        "PRICE": "\033[92m",    # Green (changed from Yellow)
        "CONFIDENCE": "\033[92m", # Green
        "STRATEGY": "\033[92m", # Green (changed from Magenta)
        "NO_SIGNAL": "\033[93m", # Yellow (changed from Dark Gray)
        "DARK_ORANGE": "\033[38;5;208m", # Dark Orange for selection prompts
        "SKY_BLUE": "\033[96m",  # Sky Blue for headers and timing
        "PURPLE": "\033[95m",    # Purple for option 3
        "YELLOW_ORANGE": "\033[38;5;214m", # Yellow-Orange for table headers
        "GREEN_OPTION": "\033[92m", # Green for option 1
        "SIGNAL_FOUND": "\033[92m", # Green when signal found
        "SIGNAL_NOT_FOUND": "\033[93m", # Yellow when signal not found
        "PROCESSING_TIME": "\033[96m", # Sky blue for processing time
        "SCAN_COMPLETED": "\033[92m", # Green for scan completed
        "NEXT_SCAN": "\033[96m", # Sky blue for next scan time
        "STOP_MESSAGE": "\033[93m", # Yellow for stop message
        "THANK_YOU": "\033[92m", # Green for thank you message
        "FINAL_MESSAGE": "\033[96m" # Sky blue for final message
    },
    "ICONS": {
        "DATE": "📅",
        "TIME": "🕐",
        "PAIR": "💱",
        "PRICE": "💰",
        "BUY": "📈",
        "SELL": "📉",
        "CONFIDENCE": "🎯",
        "STRATEGY": "🔧",
        "NO_SIGNAL": "❌",
        "ML": "🧠",
        "RULE": "📊"
    },
    "TABLE_WIDTH": 80,
    "DECIMAL_PLACES": 5,
    "SIGNAL_TABLE": {
        "COLUMN_WIDTHS": [12, 10, 12, 12, 10, 12, 15],
        "HEADERS": ["Date", "Time", "Pair", "Price", "Signal", "Confidence", "Strategy"],
        "TOTAL_WIDTH": 95
    }
}

# Advanced Signal Generator Configuration
ADVANCED_SIGNAL_CONFIG = {
    "TIMEFRAMES": ["M1", "M5", "M15", "M30", "H1"],
    "TIMEFRAME_NAMES": {
        "M1": "1 Minute",
        "M5": "5 Minutes",
        "M15": "15 Minutes",
        "M30": "30 Minutes",
        "H1": "1 Hour"
    },
    "DEFAULT_ANALYSIS_DAYS": 5,
    "MIN_ANALYSIS_DAYS": 3,
    "MAX_ANALYSIS_DAYS": 10,
    "DEFAULT_START_TIME": "19:00",
    "DEFAULT_END_TIME": "22:00",
    "CANDLE_STRENGTH_THRESHOLD": 0.5,  # Minimum body size as % of candle range
    "MIN_PATTERN_CONFIDENCE": 0.2,  # 20% - more lenient to show more signals
    "SCORING_WEIGHTS": {
        "PATTERN_CONSISTENCY": 60,  # Base score for consistent pattern
        "TREND_CONFIRMATION": 25,   # Trend alignment bonus
        "CANDLE_STRENGTH": 15       # Strong candle body bonus
    },
    "FILTERS": {
        "USE_TREND_FILTER": True,
        "USE_STRUCTURE_FILTER": False,  # Removed to be less strict
        "USE_RSI_FILTER": False,        # Removed to be less strict
        "USE_MACD_FILTER": False,       # Removed to be less strict
        "USE_STRENGTH_FILTER": True
    }
}

# Backtesting Configuration
BACKTEST_CONFIG = {
    "DEFAULT_CANDLES": 1000,
    "MAX_CANDLES": 10000,
    "WIN_THRESHOLD": 0.0001,  # Minimum price movement to consider a win (1 pip for most pairs)
    "RESULTS_PRECISION": 4
}
