#!/usr/bin/env python3
"""
Simple test script to verify the trading bot works correctly
"""

import asyncio
from datetime import datetime

def test_timing_functions():
    """Test the timing calculation functions"""
    print("🕐 Testing Timing Functions...")
    
    try:
        from trading_bot_launcher import calculate_next_candle_time, calculate_optimal_wait_time
        
        # Test 1-minute timeframe
        time_to_next, next_candle = calculate_next_candle_time(60)
        optimal_wait, _ = calculate_optimal_wait_time(60, 1.5)
        
        print(f"  ✅ 1-minute: {time_to_next:.1f}s to next candle, optimal wait: {optimal_wait:.1f}s")
        print(f"  📅 Next candle time: {next_candle.strftime('%H:%M:%S')}")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
        return False

def test_countdown_timer():
    """Test the countdown timer"""
    print("\n⏰ Testing Countdown Timer...")
    
    try:
        from trading_bot_launcher import show_countdown_timer
        
        print("  Testing 3-second countdown:")
        show_countdown_timer(3, "Test countdown")
        print("  ✅ Countdown timer working!")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {str(e)}")
        return False

def test_imports():
    """Test that all required modules can be imported"""
    print("\n📦 Testing Imports...")
    
    try:
        from trading_bot_launcher import (
            QUOTEX_EMAIL, QUOTEX_PASSWORD, QUOTEX_OTC_PAIRS, 
            connect_to_quotex, show_countdown_timer
        )
        from strategy_engine import StrategyEngine
        from utils import print_colored
        
        print("  ✅ All imports successful!")
        return True
    except Exception as e:
        print(f"  ❌ Import error: {str(e)}")
        return False

async def main():
    """Run basic tests"""
    print("🧪 Basic Bot Testing")
    print("=" * 50)
    
    results = []
    
    # Test imports
    results.append(test_imports())
    
    # Test timing functions
    results.append(test_timing_functions())
    
    # Test countdown timer
    results.append(test_countdown_timer())
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print("=" * 50)
    
    if all(results):
        print("✅ ALL BASIC TESTS PASSED!")
        print("\n🚀 Your bot is ready to use!")
        print("Run: python trading_bot_launcher.py")
    else:
        print("❌ Some tests failed")
        print("Please check the errors above")

if __name__ == "__main__":
    asyncio.run(main())
