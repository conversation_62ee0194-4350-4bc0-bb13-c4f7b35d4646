#!/usr/bin/env python3
"""
Complete Quotex Trading Solution
Combines HTTP API (working) with trading framework for bot integration
"""

import asyncio
import time
import json
import logging
from datetime import datetime
from pyquotex.http.login import Login
from pyquotex.http.settings import Settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuotexTradingSystem:
    def __init__(self, email, password, lang="pt", demo_mode=True):
        self.email = email
        self.password = password
        self.lang = lang
        self.demo_mode = demo_mode
        self.base_url = "https://market-qx.pro"
        self.is_authenticated = False
        
        # Mock API object for HTTP modules
        self.api_mock = type('obj', (object,), {
            'lang': lang,
            'https_url': self.base_url,
            'host': 'market-qx.pro',
            'resource_path': '.',
            'user_data_dir': '.',
            'username': email,
            'password': password,
            'session_data': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'cookies': None,
                'token': None
            }
        })()
        
        # Initialize HTTP modules
        self.login_module = Login(self.api_mock)
        self.settings_module = Settings(self.api_mock)
        
        # Trading state
        self.current_balance = 0.0
        self.account_info = {}
        
    async def connect(self):
        """Connect and authenticate with Quotex"""
        try:
            print("🚀 Starting Quotex Trading System...")
            print(f"📧 Email: {self.email}")
            print(f"🔵 Mode: {'Demo' if self.demo_mode else 'Live'} Account")
            
            # Authenticate
            status, message = await self.login_module(self.email, self.password)
            
            if status:
                print("✅ Authentication successful!")
                
                # Get cookies from login response
                if hasattr(self.login_module, 'response') and self.login_module.response:
                    cookies = self.login_module.response.cookies
                    cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                    self.api_mock.session_data["cookies"] = cookie_string
                
                self.is_authenticated = True
                
                # Get account information
                await self.update_account_info()
                
                return True
            else:
                print(f"❌ Authentication failed: {message}")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    async def update_account_info(self):
        """Update account information and balance"""
        try:
            if not self.is_authenticated:
                return False
            
            # Get settings which includes balance and account info
            settings_data = self.settings_module.get_settings()
            
            if isinstance(settings_data, dict) and 'data' in settings_data:
                self.account_info = settings_data['data']
                
                # Update balance based on account mode
                if self.demo_mode:
                    self.current_balance = float(self.account_info.get('demoBalance', 0))
                else:
                    self.current_balance = float(self.account_info.get('liveBalance', 0))
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating account info: {e}")
            return False
    
    def get_balance(self):
        """Get current account balance"""
        return self.current_balance
    
    def get_account_info(self):
        """Get complete account information"""
        return {
            'balance': self.current_balance,
            'currency': self.account_info.get('currencyCode', 'USD'),
            'currency_symbol': self.account_info.get('currencySymbol', '$'),
            'user_id': self.account_info.get('nickname', 'Unknown'),
            'email': self.account_info.get('email', 'Unknown'),
            'country': self.account_info.get('countryName', 'Unknown'),
            'is_demo': self.demo_mode,
            'is_verified': self.account_info.get('is_verified_profile', False),
            'live_balance': float(self.account_info.get('liveBalance', 0)),
            'demo_balance': float(self.account_info.get('demoBalance', 0))
        }
    
    def display_account_status(self):
        """Display current account status"""
        info = self.get_account_info()
        
        print("\n" + "="*60)
        print("📊 QUOTEX ACCOUNT STATUS")
        print("="*60)
        print(f"👤 User: {info['user_id']}")
        print(f"📧 Email: {info['email']}")
        print(f"🌍 Country: {info['country']}")
        print(f"✅ Verified: {'Yes' if info['is_verified'] else 'No'}")
        print("-"*60)
        print(f"💼 Current Mode: {'Demo' if info['is_demo'] else 'Live'} Account")
        print(f"💰 Current Balance: {info['currency_symbol']}{info['balance']:.2f} {info['currency']}")
        print("-"*60)
        print(f"🔴 Live Balance: {info['currency_symbol']}{info['live_balance']:.2f} {info['currency']}")
        print(f"🔵 Demo Balance: {info['currency_symbol']}{info['demo_balance']:.2f} {info['currency']}")
        print("="*60)
    
    async def process_trading_signal(self, signal):
        """
        Process a trading signal
        
        Args:
            signal (dict): Trading signal with format:
                {
                    'asset': 'EURUSD_otc',
                    'direction': 'call',  # 'call' for up, 'put' for down
                    'amount': 10.0,
                    'duration': 60,  # seconds
                    'confidence': 0.85,  # optional
                    'timestamp': **********  # optional
                }
        
        Returns:
            dict: Processing result
        """
        try:
            print(f"\n🎯 Processing Trading Signal:")
            print(f"   Asset: {signal.get('asset', 'Unknown')}")
            print(f"   Direction: {signal.get('direction', 'Unknown').upper()}")
            print(f"   Amount: ${signal.get('amount', 0):.2f}")
            print(f"   Duration: {signal.get('duration', 0)}s")
            
            if 'confidence' in signal:
                print(f"   Confidence: {signal['confidence']*100:.1f}%")
            
            # Validate signal
            required_fields = ['asset', 'direction', 'amount', 'duration']
            for field in required_fields:
                if field not in signal:
                    raise ValueError(f"Missing required field: {field}")
            
            # Check balance
            await self.update_account_info()
            
            if self.current_balance < signal['amount']:
                return {
                    'success': False,
                    'error': f"Insufficient balance. Required: ${signal['amount']:.2f}, Available: ${self.current_balance:.2f}"
                }
            
            # Since WebSocket trading is blocked, provide manual trading instructions
            print(f"\n📋 MANUAL TRADING INSTRUCTIONS:")
            print(f"   1. Open Quotex web platform: {self.base_url}/{self.lang}/trade")
            print(f"   2. Select asset: {signal['asset']}")
            print(f"   3. Set amount: ${signal['amount']:.2f}")
            print(f"   4. Set duration: {signal['duration']}s")
            print(f"   5. Click {'HIGHER' if signal['direction'] == 'call' else 'LOWER'}")
            print(f"   6. Execute trade immediately!")
            
            # Log the signal for future automated processing
            signal_log = {
                'timestamp': time.time(),
                'signal': signal,
                'account_balance': self.current_balance,
                'status': 'manual_execution_required'
            }
            
            # Save signal to file for tracking
            with open('trading_signals.json', 'a') as f:
                f.write(json.dumps(signal_log) + '\n')
            
            return {
                'success': True,
                'status': 'manual_execution_required',
                'instructions': 'Manual trading instructions provided',
                'signal_logged': True
            }
            
        except Exception as e:
            logger.error(f"Error processing signal: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def monitor_balance(self, interval=30):
        """Monitor account balance changes"""
        print(f"\n📊 Starting balance monitoring (every {interval}s)...")
        
        previous_balance = self.current_balance
        
        while True:
            try:
                await self.update_account_info()
                
                if self.current_balance != previous_balance:
                    change = self.current_balance - previous_balance
                    symbol = "📈" if change > 0 else "📉"
                    print(f"{symbol} Balance changed: ${previous_balance:.2f} → ${self.current_balance:.2f} ({change:+.2f})")
                    previous_balance = self.current_balance
                
                await asyncio.sleep(interval)
                
            except KeyboardInterrupt:
                print("\n⏹️ Balance monitoring stopped")
                break
            except Exception as e:
                logger.error(f"Error monitoring balance: {e}")
                await asyncio.sleep(interval)

class TradingBot:
    """Trading bot that integrates with external signal providers"""
    
    def __init__(self, quotex_system):
        self.quotex = quotex_system
        self.is_running = False
        
    async def start(self):
        """Start the trading bot"""
        if not self.quotex.is_authenticated:
            print("❌ Quotex system not connected. Please connect first.")
            return False
        
        print("\n🤖 Starting Trading Bot...")
        print("📡 Waiting for trading signals...")
        print("💡 Send signals using: bot.process_signal(signal_dict)")
        
        self.is_running = True
        return True
    
    async def process_signal(self, signal):
        """Process a trading signal through the bot"""
        if not self.is_running:
            return {'success': False, 'error': 'Bot not running'}
        
        return await self.quotex.process_trading_signal(signal)
    
    def stop(self):
        """Stop the trading bot"""
        self.is_running = False
        print("🛑 Trading bot stopped")

# Example usage and testing
async def main():
    """Main function demonstrating the complete solution"""
    
    # Initialize the trading system
    quotex = QuotexTradingSystem(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt",
        demo_mode=True  # Use demo account
    )
    
    try:
        # Connect to Quotex
        if await quotex.connect():
            # Display account status
            quotex.display_account_status()
            
            # Initialize trading bot
            bot = TradingBot(quotex)
            await bot.start()
            
            # Example trading signals
            test_signals = [
                {
                    'asset': 'EURUSD_otc',
                    'direction': 'call',
                    'amount': 1.0,
                    'duration': 60,
                    'confidence': 0.85
                },
                {
                    'asset': 'GBPUSD_otc',
                    'direction': 'put',
                    'amount': 2.0,
                    'duration': 120,
                    'confidence': 0.78
                }
            ]
            
            # Process test signals
            for i, signal in enumerate(test_signals, 1):
                print(f"\n🔄 Processing test signal {i}/{len(test_signals)}")
                result = await bot.process_signal(signal)
                print(f"📊 Result: {result}")
                
                if i < len(test_signals):
                    print("⏳ Waiting 5 seconds before next signal...")
                    await asyncio.sleep(5)
            
            print("\n✅ Demo completed successfully!")
            print("\n📝 Next Steps:")
            print("   1. Integrate with your signal provider")
            print("   2. Use bot.process_signal(signal) to send signals")
            print("   3. Monitor balance with quotex.monitor_balance()")
            print("   4. Check trading_signals.json for logged signals")
            
        else:
            print("❌ Failed to connect to Quotex")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
