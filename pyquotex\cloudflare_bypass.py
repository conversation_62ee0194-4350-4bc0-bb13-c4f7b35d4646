#!/usr/bin/env python3
"""
Cloudflare Bypass for Quotex WebSocket Connection
Uses browser automation to get valid session cookies
"""

import asyncio
import time
import json
from playwright.async_api import async_playwright

class CloudflareBypass:
    def __init__(self, email, password, headless=True):
        self.email = email
        self.password = password
        self.headless = headless
        self.cookies = None
        self.user_agent = None
        
    async def get_session_data(self):
        """Get valid session cookies and user agent by bypassing Cloudflare"""
        async with async_playwright() as p:
            # Launch browser with stealth settings
            browser = await p.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Create context with realistic settings
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            page = await context.new_page()
            
            try:
                print("🌐 Navigating to Quotex login page...")
                await page.goto("https://market-qx.pro/pt/sign-in/", timeout=60000)
                
                # Wait for Cloudflare challenge to complete
                print("⏳ Waiting for Cloudflare challenge...")
                await page.wait_for_load_state("networkidle", timeout=60000)
                
                # Additional wait for any remaining challenges
                await asyncio.sleep(5)
                
                # Check if we're still on Cloudflare page
                page_content = await page.content()
                if "cloudflare" in page_content.lower() or "just a moment" in page_content.lower():
                    print("⏳ Still processing Cloudflare challenge, waiting more...")
                    await asyncio.sleep(10)
                
                # Look for login form
                print("🔍 Looking for login form...")
                
                # Try to click the login tab if needed
                try:
                    login_tab = await page.wait_for_selector('a[data-value="1"]', timeout=5000)
                    if login_tab:
                        await login_tab.click()
                        await asyncio.sleep(1)
                except:
                    pass
                
                # Wait for email input
                email_input = await page.wait_for_selector('#tab-1 input[name="email"], input[name="email"]', timeout=10000)
                password_input = await page.wait_for_selector('#tab-1 input[name="password"], input[name="password"]', timeout=5000)
                
                if email_input and password_input:
                    print("📝 Filling login credentials...")
                    
                    # Clear and fill email
                    await email_input.click()
                    await email_input.fill("")
                    await email_input.type(self.email, delay=100)
                    
                    # Clear and fill password
                    await password_input.click()
                    await password_input.fill("")
                    await password_input.type(self.password, delay=100)
                    
                    # Wait a bit before submitting
                    await asyncio.sleep(2)
                    
                    # Submit the form
                    print("🚀 Submitting login form...")
                    submit_button = await page.query_selector('button[type="submit"], input[type="submit"]')
                    if submit_button:
                        await submit_button.click()
                    else:
                        # Try pressing Enter on password field
                        await password_input.press('Enter')
                    
                    # Wait for login response
                    print("⏳ Waiting for login response...")
                    await page.wait_for_load_state("networkidle", timeout=30000)
                    
                    # Check if login was successful
                    current_url = page.url
                    print(f"📍 Current URL: {current_url}")
                    
                    if "trade" in current_url or "cabinet" in current_url:
                        print("✅ Login successful!")
                        
                        # Get cookies
                        cookies = await context.cookies()
                        self.cookies = {cookie['name']: cookie['value'] for cookie in cookies}
                        
                        # Get user agent
                        self.user_agent = await page.evaluate("navigator.userAgent")
                        
                        print(f"🍪 Got {len(cookies)} cookies")
                        print(f"🤖 User Agent: {self.user_agent}")
                        
                        # Save session data
                        session_data = {
                            'cookies': self.cookies,
                            'user_agent': self.user_agent,
                            'timestamp': time.time()
                        }
                        
                        with open('session_data.json', 'w') as f:
                            json.dump(session_data, f, indent=2)
                        
                        print("💾 Session data saved to session_data.json")
                        return session_data
                    else:
                        print("❌ Login failed - not redirected to trade page")
                        
                        # Check for error messages
                        error_elements = await page.query_selector_all('.error, .alert-danger, .hint--danger')
                        for element in error_elements:
                            error_text = await element.text_content()
                            if error_text and error_text.strip():
                                print(f"❌ Error: {error_text.strip()}")
                        
                        return None
                else:
                    print("❌ Login form not found")
                    return None
                    
            except Exception as e:
                print(f"❌ Error during bypass: {e}")
                import traceback
                traceback.print_exc()
                return None
            
            finally:
                await browser.close()

async def test_bypass():
    """Test the Cloudflare bypass"""
    bypass = CloudflareBypass(
        email="<EMAIL>",
        password="Uz2309##2309",
        headless=False  # Set to True for headless mode
    )
    
    session_data = await bypass.get_session_data()
    
    if session_data:
        print("🎉 Cloudflare bypass successful!")
        return session_data
    else:
        print("💥 Cloudflare bypass failed!")
        return None

if __name__ == "__main__":
    result = asyncio.run(test_bypass())
    if result:
        print("✅ Ready to use session data for WebSocket connection!")
    else:
        print("❌ Failed to get session data!")
