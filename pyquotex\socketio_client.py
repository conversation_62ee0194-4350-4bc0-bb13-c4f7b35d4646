#!/usr/bin/env python3
"""
Socket.IO WebSocket Client for Quotex
Replaces the regular WebSocket client with Socket.IO support
"""

import asyncio
import socketio
import time
import json
import logging
from pyquotex.http.login import Login
from pyquotex.http.settings import Settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuotexSocketIOClient:
    """Socket.IO client for Quotex WebSocket connection"""
    
    def __init__(self, email, password, lang="pt"):
        self.email = email
        self.password = password
        self.lang = lang
        self.base_url = "https://market-qx.pro"
        self.socket_url = "wss://ws2.market-qx.pro"
        
        # Initialize Socket.IO client with Engine.IO version 3
        self.sio = socketio.AsyncClient(
            logger=True,
            engineio_logger=True,
            reconnection=True,
            reconnection_attempts=5,
            reconnection_delay=5,
            engineio_version=3  # Force Engine.IO version 3 to match Quotex
        )
        
        self.is_connected = False
        self.is_authenticated = False
        
        # Mock API object for HTTP modules
        self.api_mock = type('obj', (object,), {
            'lang': lang,
            'https_url': self.base_url,
            'host': 'market-qx.pro',
            'resource_path': '.',
            'user_data_dir': '.',
            'username': email,
            'password': password,
            'session_data': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'cookies': None,
                'token': None
            }
        })()
        
        # Initialize HTTP modules
        self.login_module = Login(self.api_mock)
        self.settings_module = Settings(self.api_mock)
        
        # Setup Socket.IO event handlers
        self.setup_event_handlers()
        
        # Data storage
        self.candles_data = {}
        self.balance_data = {}
        self.trade_results = {}
        
    def setup_event_handlers(self):
        """Setup Socket.IO event handlers"""
        
        @self.sio.event
        async def connect():
            print("✅ Socket.IO connected successfully!")
            self.is_connected = True
            
            # Send authentication after connection
            await self.authenticate_websocket()
        
        @self.sio.event
        async def disconnect():
            print("❌ Socket.IO disconnected")
            self.is_connected = False
            self.is_authenticated = False
        
        @self.sio.event
        async def connect_error(data):
            print(f"❌ Socket.IO connection error: {data}")
        
        @self.sio.event
        async def message(data):
            """Handle incoming messages"""
            try:
                print(f"📨 Received message: {data}")
                await self.handle_message(data)
            except Exception as e:
                logger.error(f"Message handling error: {e}")
        
        # Handle specific events that Quotex might use
        @self.sio.event
        async def candles(data):
            """Handle candle data"""
            print(f"📊 Received candles: {data}")
            await self.handle_candles(data)
        
        @self.sio.event
        async def balance(data):
            """Handle balance updates"""
            print(f"💰 Received balance: {data}")
            await self.handle_balance(data)
        
        @self.sio.event
        async def trade_result(data):
            """Handle trade results"""
            print(f"🎯 Received trade result: {data}")
            await self.handle_trade_result(data)
    
    async def authenticate_http(self):
        """Authenticate via HTTP first"""
        try:
            print("🔐 Authenticating via HTTP...")
            
            status, message = await self.login_module(self.email, self.password)
            
            if status:
                print("✅ HTTP authentication successful!")
                
                # Get cookies from login response
                if hasattr(self.login_module, 'response') and self.login_module.response:
                    cookies = self.login_module.response.cookies
                    cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                    self.api_mock.session_data["cookies"] = cookie_string
                    print(f"🍪 Got cookies: {cookie_string[:100]}...")
                
                return True
            else:
                print(f"❌ HTTP authentication failed: {message}")
                return False
                
        except Exception as e:
            print(f"❌ HTTP authentication error: {e}")
            return False
    
    async def connect_socketio(self):
        """Connect to Socket.IO server"""
        try:
            print("🔌 Connecting to Socket.IO server...")
            
            # First authenticate via HTTP
            if not await self.authenticate_http():
                return False
            
            # Prepare headers with cookies
            headers = {
                'User-Agent': self.api_mock.session_data["user_agent"],
                'Cookie': self.api_mock.session_data.get("cookies", ""),
                'Origin': self.base_url,
                'Referer': f"{self.base_url}/{self.lang}/trade"
            }
            
            # Connect to Socket.IO with exact parameters matching browser
            await self.sio.connect(
                self.socket_url,
                headers=headers,
                transports=['websocket'],
                socketio_path='/socket.io/',
                wait_timeout=30,
                # Additional parameters to match browser behavior
                query={'EIO': '3', 'transport': 'websocket'}
            )
            
            return True
            
        except Exception as e:
            print(f"❌ Socket.IO connection error: {e}")
            return False
    
    async def authenticate_websocket(self):
        """Authenticate the WebSocket connection"""
        try:
            print("🔑 Authenticating WebSocket connection...")
            
            # Send authentication message (adapt based on Quotex protocol)
            auth_data = {
                'email': self.email,
                'password': self.password,
                'lang': self.lang,
                'cookies': self.api_mock.session_data.get("cookies")
            }
            
            await self.sio.emit('authenticate', auth_data)
            
            # Wait for authentication response
            await asyncio.sleep(2)
            
            self.is_authenticated = True
            print("✅ WebSocket authentication completed!")
            
        except Exception as e:
            print(f"❌ WebSocket authentication error: {e}")
    
    async def subscribe_candles(self, asset, period=60):
        """Subscribe to candle data for an asset"""
        try:
            if not self.is_connected:
                raise Exception("Not connected to Socket.IO")
            
            print(f"📊 Subscribing to candles for {asset} ({period}s)")
            
            # Send subscription message
            subscription_data = {
                'asset': asset,
                'period': period,
                'action': 'subscribe'
            }
            
            await self.sio.emit('subscribe_candles', subscription_data)
            
            return True
            
        except Exception as e:
            print(f"❌ Candle subscription error: {e}")
            return False
    
    async def place_trade(self, asset, direction, amount, duration):
        """Place a trade via Socket.IO"""
        try:
            if not self.is_connected or not self.is_authenticated:
                raise Exception("Not connected or authenticated")
            
            print(f"🎯 Placing trade: {direction.upper()} {asset} ${amount} {duration}s")
            
            # Prepare trade data
            trade_data = {
                'asset': asset,
                'direction': direction,  # 'call' or 'put'
                'amount': amount,
                'duration': duration,
                'timestamp': int(time.time()),
                'request_id': int(time.time() * 1000)
            }
            
            # Send trade order
            await self.sio.emit('place_trade', trade_data)
            
            print("📤 Trade order sent!")
            return True
            
        except Exception as e:
            print(f"❌ Trade placement error: {e}")
            return False
    
    async def handle_message(self, data):
        """Handle incoming Socket.IO messages"""
        try:
            if isinstance(data, str):
                data = json.loads(data)
            
            message_type = data.get('type', 'unknown')
            
            if message_type == 'candles':
                await self.handle_candles(data)
            elif message_type == 'balance':
                await self.handle_balance(data)
            elif message_type == 'trade_result':
                await self.handle_trade_result(data)
            else:
                print(f"📨 Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Message handling error: {e}")
    
    async def handle_candles(self, data):
        """Handle candle data"""
        try:
            asset = data.get('asset', 'unknown')
            candles = data.get('candles', [])
            
            if asset not in self.candles_data:
                self.candles_data[asset] = []
            
            # Store candle data
            self.candles_data[asset].extend(candles)
            
            # Keep only last 1000 candles per asset
            if len(self.candles_data[asset]) > 1000:
                self.candles_data[asset] = self.candles_data[asset][-1000:]
            
            print(f"📊 Updated candles for {asset}: {len(candles)} new candles")
            
        except Exception as e:
            logger.error(f"Candle handling error: {e}")
    
    async def handle_balance(self, data):
        """Handle balance updates"""
        try:
            self.balance_data = data
            balance = data.get('balance', 0)
            currency = data.get('currency', 'USD')
            
            print(f"💰 Balance updated: {balance} {currency}")
            
        except Exception as e:
            logger.error(f"Balance handling error: {e}")
    
    async def handle_trade_result(self, data):
        """Handle trade results"""
        try:
            trade_id = data.get('trade_id', 'unknown')
            result = data.get('result', 'unknown')
            profit = data.get('profit', 0)
            
            self.trade_results[trade_id] = data
            
            print(f"🎯 Trade {trade_id} result: {result} (Profit: {profit})")
            
        except Exception as e:
            logger.error(f"Trade result handling error: {e}")
    
    def get_candles(self, asset):
        """Get stored candle data for an asset"""
        return self.candles_data.get(asset, [])
    
    def get_balance(self):
        """Get current balance"""
        return self.balance_data.get('balance', 0)
    
    async def disconnect(self):
        """Disconnect from Socket.IO"""
        if self.is_connected:
            await self.sio.disconnect()
            print("🔌 Disconnected from Socket.IO")

# Test the Socket.IO client
async def test_socketio_client():
    """Test the Socket.IO client"""
    
    client = QuotexSocketIOClient(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt"
    )
    
    try:
        print("🚀 Testing Socket.IO connection to Quotex...")
        
        # Connect to Socket.IO
        if await client.connect_socketio():
            print("✅ Socket.IO connection successful!")
            
            # Wait for connection to stabilize
            await asyncio.sleep(5)
            
            # Test candle subscription
            await client.subscribe_candles("EURUSD_otc", 60)
            
            # Wait for candle data
            print("⏳ Waiting for candle data...")
            await asyncio.sleep(10)
            
            # Check received data
            candles = client.get_candles("EURUSD_otc")
            print(f"📊 Received {len(candles)} candles for EURUSD_otc")
            
            # Test trade placement
            await client.place_trade("EURUSD_otc", "call", 1.0, 60)
            
            # Wait for trade result
            print("⏳ Waiting for trade result...")
            await asyncio.sleep(10)
            
            print("✅ Socket.IO test completed!")
            
        else:
            print("❌ Socket.IO connection failed")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(test_socketio_client())
