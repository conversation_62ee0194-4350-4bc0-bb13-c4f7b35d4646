import asyncio
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'pyquotex'))

from pyquotex.http.login import Login
from pyquotex.api import QuotexAPI

async def test_login():
    try:
        # Create a mock API object
        class MockAPI:
            def __init__(self):
                self.lang = "pt"
        
        api = MockAPI()
        login = Login(api)
        
        print("Testing login directly...")
        result = await login("<EMAIL>", "Uz2309##2309")
        print(f"Login result: {result}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

asyncio.run(test_login())
