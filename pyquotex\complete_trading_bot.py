#!/usr/bin/env python3
"""
Complete Quotex Trading Bot
Integrates authentication, balance monitoring, signal processing, and candle data
"""

import asyncio
import time
import json
import logging
from datetime import datetime
from quotex_complete_solution import QuotexTradingSystem
from candle_data_provider import CandleDataProvider

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompleteTradingBot:
    """
    Complete trading bot that integrates all functionality:
    - Quotex authentication and balance monitoring
    - Signal processing and trade execution instructions
    - Candle data analysis
    - Trading decision support
    """
    
    def __init__(self, email, password, lang="pt", demo_mode=True):
        self.quotex = QuotexTradingSystem(email, password, lang, demo_mode)
        self.candle_provider = CandleDataProvider()
        self.is_running = False
        self.signal_queue = []
        self.trading_history = []
        
    async def start(self):
        """Start the complete trading bot"""
        print("🚀 Starting Complete Quotex Trading Bot...")
        
        # Connect to Quotex
        if not await self.quotex.connect():
            print("❌ Failed to connect to Quotex")
            return False
        
        # Display account status
        self.quotex.display_account_status()
        
        self.is_running = True
        print("✅ Trading bot is now running!")
        
        return True
    
    async def analyze_market(self, asset, period=60, count=50):
        """
        Analyze market conditions for an asset
        
        Args:
            asset (str): Asset symbol (e.g., "EURUSD_otc")
            period (int): Candle period in seconds
            count (int): Number of candles to analyze
        
        Returns:
            dict: Market analysis results
        """
        try:
            print(f"📊 Analyzing market for {asset}...")
            
            # Get candle data
            candles = await self.candle_provider.get_candles(asset, period, count)
            
            if not candles:
                return {'error': 'No candle data available'}
            
            # Analyze the candles
            analysis = self.candle_provider.analyze_candles(candles)
            
            # Add trading recommendations
            analysis['recommendations'] = self._generate_recommendations(analysis)
            analysis['asset'] = asset
            analysis['timestamp'] = time.time()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Market analysis error: {e}")
            return {'error': str(e)}
    
    def _generate_recommendations(self, analysis):
        """Generate trading recommendations based on analysis"""
        recommendations = []
        
        try:
            latest_price = analysis.get('latest_price', 0)
            trend = analysis.get('trend', 'UNKNOWN')
            price_change_percent = analysis.get('price_change_percent', 0)
            support = analysis.get('support', 0)
            resistance = analysis.get('resistance', 0)
            
            # Trend-based recommendations
            if trend == 'UP' and price_change_percent > 0.05:
                recommendations.append({
                    'type': 'CALL',
                    'confidence': 0.7,
                    'reason': 'Strong upward trend with positive momentum'
                })
            elif trend == 'DOWN' and price_change_percent < -0.05:
                recommendations.append({
                    'type': 'PUT',
                    'confidence': 0.7,
                    'reason': 'Strong downward trend with negative momentum'
                })
            
            # Support/Resistance recommendations
            if latest_price <= support * 1.001:  # Near support
                recommendations.append({
                    'type': 'CALL',
                    'confidence': 0.6,
                    'reason': 'Price near support level - potential bounce'
                })
            elif latest_price >= resistance * 0.999:  # Near resistance
                recommendations.append({
                    'type': 'PUT',
                    'confidence': 0.6,
                    'reason': 'Price near resistance level - potential reversal'
                })
            
            # Volatility-based recommendations
            volatility = analysis.get('volatility', 0)
            if volatility > 0.001:  # High volatility
                recommendations.append({
                    'type': 'CAUTION',
                    'confidence': 0.8,
                    'reason': 'High volatility detected - trade with caution'
                })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Recommendation generation error: {e}")
            return []
    
    async def process_external_signal(self, signal):
        """
        Process a signal from external source (your signal provider)
        
        Args:
            signal (dict): Signal with format:
                {
                    'asset': 'EURUSD_otc',
                    'direction': 'call',  # 'call' or 'put'
                    'amount': 10.0,
                    'duration': 60,
                    'confidence': 0.85,
                    'source': 'my_signal_provider'
                }
        """
        try:
            print(f"\n🎯 Processing external signal from {signal.get('source', 'unknown')}")
            
            # Validate signal
            required_fields = ['asset', 'direction', 'amount', 'duration']
            for field in required_fields:
                if field not in signal:
                    return {'success': False, 'error': f'Missing field: {field}'}
            
            # Get market analysis for the asset
            analysis = await self.analyze_market(signal['asset'])
            
            # Combine signal with market analysis
            enhanced_signal = signal.copy()
            enhanced_signal['market_analysis'] = analysis
            enhanced_signal['timestamp'] = time.time()
            
            # Check if market analysis supports the signal
            recommendations = analysis.get('recommendations', [])
            signal_supported = False
            
            for rec in recommendations:
                if rec['type'].lower() == signal['direction'].lower():
                    signal_supported = True
                    enhanced_signal['market_support'] = rec
                    break
            
            if not signal_supported:
                print("⚠️ Warning: Market analysis doesn't strongly support this signal")
                enhanced_signal['market_support'] = {'type': 'NEUTRAL', 'confidence': 0.5}
            
            # Process the signal through Quotex system
            result = await self.quotex.process_trading_signal(enhanced_signal)
            
            # Log the complete trading decision
            trading_record = {
                'signal': enhanced_signal,
                'result': result,
                'timestamp': time.time(),
                'balance_before': self.quotex.get_balance()
            }
            
            self.trading_history.append(trading_record)
            
            # Save to file
            with open('complete_trading_log.json', 'a') as f:
                f.write(json.dumps(trading_record) + '\n')
            
            return result
            
        except Exception as e:
            logger.error(f"Signal processing error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def auto_analyze_assets(self, assets, interval=300):
        """
        Continuously analyze multiple assets and provide insights
        
        Args:
            assets (list): List of assets to analyze
            interval (int): Analysis interval in seconds
        """
        print(f"📊 Starting auto-analysis for {len(assets)} assets (every {interval}s)")
        
        while self.is_running:
            try:
                for asset in assets:
                    analysis = await self.analyze_market(asset)
                    
                    if 'error' not in analysis:
                        recommendations = analysis.get('recommendations', [])
                        
                        if recommendations:
                            print(f"\n💡 {asset} Analysis:")
                            print(f"   Price: {analysis.get('latest_price', 'N/A')}")
                            print(f"   Trend: {analysis.get('trend', 'N/A')}")
                            print(f"   Change: {analysis.get('price_change_percent', 0):.3f}%")
                            
                            for rec in recommendations:
                                if rec['type'] in ['CALL', 'PUT']:
                                    print(f"   🎯 Recommendation: {rec['type']} (Confidence: {rec['confidence']*100:.0f}%)")
                                    print(f"      Reason: {rec['reason']}")
                    
                    await asyncio.sleep(2)  # Small delay between assets
                
                await asyncio.sleep(interval)
                
            except KeyboardInterrupt:
                print("\n⏹️ Auto-analysis stopped")
                break
            except Exception as e:
                logger.error(f"Auto-analysis error: {e}")
                await asyncio.sleep(interval)
    
    async def monitor_balance_changes(self, interval=30):
        """Monitor balance changes and trading results"""
        print(f"💰 Starting balance monitoring (every {interval}s)")
        
        previous_balance = self.quotex.get_balance()
        
        while self.is_running:
            try:
                await self.quotex.update_account_info()
                current_balance = self.quotex.get_balance()
                
                if current_balance != previous_balance:
                    change = current_balance - previous_balance
                    symbol = "📈" if change > 0 else "📉"
                    print(f"{symbol} Balance: ${previous_balance:.2f} → ${current_balance:.2f} ({change:+.2f})")
                    previous_balance = current_balance
                
                await asyncio.sleep(interval)
                
            except KeyboardInterrupt:
                print("\n⏹️ Balance monitoring stopped")
                break
            except Exception as e:
                logger.error(f"Balance monitoring error: {e}")
                await asyncio.sleep(interval)
    
    def stop(self):
        """Stop the trading bot"""
        self.is_running = False
        print("🛑 Trading bot stopped")
    
    def get_trading_summary(self):
        """Get summary of trading activity"""
        total_signals = len(self.trading_history)
        successful_signals = sum(1 for record in self.trading_history if record['result'].get('success'))
        
        return {
            'total_signals_processed': total_signals,
            'successful_signals': successful_signals,
            'success_rate': (successful_signals / total_signals * 100) if total_signals > 0 else 0,
            'current_balance': self.quotex.get_balance(),
            'account_info': self.quotex.get_account_info()
        }

# Example usage and integration guide
async def demo_complete_bot():
    """Demonstrate the complete trading bot functionality"""
    
    # Initialize the complete trading bot
    bot = CompleteTradingBot(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt",
        demo_mode=True
    )
    
    try:
        # Start the bot
        if await bot.start():
            
            # Example 1: Analyze market conditions
            print("\n" + "="*60)
            print("📊 MARKET ANALYSIS DEMO")
            print("="*60)
            
            analysis = await bot.analyze_market("EURUSD_otc", period=60, count=30)
            if 'error' not in analysis:
                print(f"Asset: {analysis['asset']}")
                print(f"Price: {analysis.get('latest_price', 'N/A')}")
                print(f"Trend: {analysis.get('trend', 'N/A')}")
                print(f"Support: {analysis.get('support', 'N/A')}")
                print(f"Resistance: {analysis.get('resistance', 'N/A')}")
                
                recommendations = analysis.get('recommendations', [])
                if recommendations:
                    print("Recommendations:")
                    for rec in recommendations:
                        print(f"  - {rec['type']}: {rec['reason']} (Confidence: {rec['confidence']*100:.0f}%)")
            
            # Example 2: Process external signals
            print("\n" + "="*60)
            print("🎯 SIGNAL PROCESSING DEMO")
            print("="*60)
            
            # Simulate signals from your signal provider
            test_signals = [
                {
                    'asset': 'EURUSD_otc',
                    'direction': 'call',
                    'amount': 1.0,
                    'duration': 60,
                    'confidence': 0.85,
                    'source': 'demo_signal_provider'
                },
                {
                    'asset': 'GBPUSD_otc',
                    'direction': 'put',
                    'amount': 2.0,
                    'duration': 120,
                    'confidence': 0.78,
                    'source': 'demo_signal_provider'
                }
            ]
            
            for signal in test_signals:
                result = await bot.process_external_signal(signal)
                print(f"Signal result: {result.get('status', 'unknown')}")
                await asyncio.sleep(2)
            
            # Show trading summary
            print("\n" + "="*60)
            print("📈 TRADING SUMMARY")
            print("="*60)
            
            summary = bot.get_trading_summary()
            print(f"Total signals processed: {summary['total_signals_processed']}")
            print(f"Successful signals: {summary['successful_signals']}")
            print(f"Success rate: {summary['success_rate']:.1f}%")
            print(f"Current balance: ${summary['current_balance']:.2f}")
            
            print("\n✅ Demo completed successfully!")
            
        else:
            print("❌ Failed to start trading bot")
            
    except Exception as e:
        print(f"❌ Demo error: {e}")
    
    finally:
        bot.stop()

if __name__ == "__main__":
    print("🤖 Complete Quotex Trading Bot")
    print("="*50)
    try:
        asyncio.run(demo_complete_bot())
    except Exception as e:
        print(f"❌ Main error: {e}")
        import traceback
        traceback.print_exc()
