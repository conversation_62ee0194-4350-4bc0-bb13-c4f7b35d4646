import asyncio
import logging
from pyquotex.stable_api import Quotex

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def main():
    client = Quotex(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt"  # or "en"
    )
    
    # Enable debug mode
    client.debug_ws_enable = True
    
    try:
        print("Attempting to connect...")
        await client.connect()
        print("Connected successfully!")
        
        print("Getting balance...")
        balance = await client.get_balance()
        print(f"Balance: {balance}")
        
    except Exception as e:
        print(f"Error occurred: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if client:
            await client.close()

# Entry point
asyncio.run(main())
