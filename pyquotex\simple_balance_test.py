import asyncio
import requests
from pyquotex.http.login import Login
from pyquotex.http.settings import Settings

async def test_balance_http():
    try:
        print("Step 1: Creating login instance...")
        
        # Create a mock API object for login
        class MockAPI:
            def __init__(self):
                self.lang = "pt"
                self.https_url = "https://market-qx.pro"
                self.host = "market-qx.pro"
                self.resource_path = "."
                self.user_data_dir = "."
                self.username = "<EMAIL>"
                self.password = "Uz2309##2309"
                self.session_data = {
                    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "cookies": None,
                    "token": None
                }
        
        api = MockAPI()
        login = Login(api)
        
        print("Step 2: Attempting login...")
        status, msg = await login("<EMAIL>", "Uz2309##2309")
        
        print(f"Login result: {status}, Message: {msg}")
        
        if status:
            print("Step 3: Login successful! Getting cookies...")
            
            # Get cookies from the login session
            if hasattr(login, 'response') and login.response:
                cookies = login.response.cookies
                cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                api.session_data["cookies"] = cookie_string
                print(f"Cookies obtained: {cookie_string[:100]}...")
            
            print("Step 4: Attempting to get settings/balance...")
            
            # Create settings instance
            settings = Settings(api)
            
            try:
                settings_data = settings.get_settings()
                print(f"Settings data type: {type(settings_data)}")
                print(f"Settings data: {settings_data}")
                
                # Look for balance information
                if isinstance(settings_data, dict):
                    for key, value in settings_data.items():
                        if any(word in key.lower() for word in ['balance', 'money', 'amount', 'demo', 'real']):
                            print(f"Found: {key} = {value}")
                
                return True
                
            except Exception as e:
                print(f"Error getting settings: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("Login failed!")
            return False
            
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_balance_http())
    print(f"Test result: {'SUCCESS' if result else 'FAILED'}")
