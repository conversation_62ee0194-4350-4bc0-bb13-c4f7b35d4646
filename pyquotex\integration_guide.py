#!/usr/bin/env python3
"""
Quotex Trading Bot Integration Guide
Complete guide for integrating your signal provider with the Quotex trading bot
"""

import asyncio
import json
from complete_trading_bot import CompleteTradingBot

class SignalProviderIntegration:
    """
    Example integration class showing how to connect your signal provider
    with the Quotex trading bot
    """
    
    def __init__(self, quotex_email, quotex_password, demo_mode=True):
        # Initialize the trading bot
        self.bot = CompleteTradingBot(
            email=quotex_email,
            password=quotex_password,
            lang="pt",
            demo_mode=demo_mode
        )
        
        self.is_running = False
    
    async def start_integration(self):
        """Start the integration system"""
        print("🔗 Starting Signal Provider Integration...")
        
        # Start the trading bot
        if await self.bot.start():
            self.is_running = True
            print("✅ Integration system ready!")
            return True
        else:
            print("❌ Failed to start integration system")
            return False
    
    async def process_signal_from_your_provider(self, raw_signal):
        """
        Process a signal from your signal provider
        
        Args:
            raw_signal: Your signal format (adapt this to your provider's format)
        
        Example raw_signal formats you might receive:
        
        Format 1 - Simple:
        {
            "pair": "EUR/USD",
            "action": "BUY",
            "amount": 10,
            "expiry": "1M"
        }
        
        Format 2 - Detailed:
        {
            "symbol": "EURUSD",
            "direction": "CALL",
            "investment": 25.0,
            "duration": 60,
            "confidence": 85,
            "strategy": "RSI_MACD"
        }
        """
        
        try:
            # Convert your signal format to bot format
            standardized_signal = self._convert_signal_format(raw_signal)
            
            # Process through the trading bot
            result = await self.bot.process_external_signal(standardized_signal)
            
            return result
            
        except Exception as e:
            print(f"❌ Signal processing error: {e}")
            return {'success': False, 'error': str(e)}
    
    def _convert_signal_format(self, raw_signal):
        """
        Convert your signal provider's format to the bot's standard format
        
        Adapt this method to match your signal provider's format
        """
        
        # Example conversion for different formats
        if 'pair' in raw_signal:
            # Format 1 conversion
            asset = self._convert_pair_to_quotex_asset(raw_signal['pair'])
            direction = 'call' if raw_signal['action'].upper() == 'BUY' else 'put'
            duration = self._convert_expiry_to_seconds(raw_signal['expiry'])
            
            return {
                'asset': asset,
                'direction': direction,
                'amount': float(raw_signal['amount']),
                'duration': duration,
                'source': 'your_signal_provider'
            }
        
        elif 'symbol' in raw_signal:
            # Format 2 conversion
            asset = self._convert_symbol_to_quotex_asset(raw_signal['symbol'])
            
            return {
                'asset': asset,
                'direction': raw_signal['direction'].lower(),
                'amount': float(raw_signal['investment']),
                'duration': int(raw_signal['duration']),
                'confidence': raw_signal.get('confidence', 50) / 100,
                'source': 'your_signal_provider',
                'strategy': raw_signal.get('strategy', 'unknown')
            }
        
        else:
            raise ValueError("Unknown signal format")
    
    def _convert_pair_to_quotex_asset(self, pair):
        """Convert pair format (EUR/USD) to Quotex asset (EURUSD_otc)"""
        pair_map = {
            'EUR/USD': 'EURUSD_otc',
            'GBP/USD': 'GBPUSD_otc',
            'USD/JPY': 'USDJPY_otc',
            'AUD/USD': 'AUDUSD_otc',
            'USD/CAD': 'USDCAD_otc',
            'USD/CHF': 'USDCHF_otc',
            'NZD/USD': 'NZDUSD_otc',
            'EUR/JPY': 'EURJPY_otc',
            'GBP/JPY': 'GBPJPY_otc',
            'EUR/GBP': 'EURGBP_otc'
        }
        
        return pair_map.get(pair, pair.replace('/', '') + '_otc')
    
    def _convert_symbol_to_quotex_asset(self, symbol):
        """Convert symbol format (EURUSD) to Quotex asset (EURUSD_otc)"""
        if not symbol.endswith('_otc'):
            return symbol + '_otc'
        return symbol
    
    def _convert_expiry_to_seconds(self, expiry):
        """Convert expiry format (1M, 5M, etc.) to seconds"""
        expiry_map = {
            '1M': 60,
            '2M': 120,
            '3M': 180,
            '5M': 300,
            '10M': 600,
            '15M': 900,
            '30M': 1800,
            '1H': 3600
        }
        
        return expiry_map.get(expiry.upper(), 60)
    
    async def run_signal_listener(self):
        """
        Example of how to continuously listen for signals
        Replace this with your actual signal source (WebSocket, API, file, etc.)
        """
        print("👂 Starting signal listener...")
        
        # Example: Reading signals from a file (replace with your method)
        while self.is_running:
            try:
                # Method 1: Read from file
                signals = self._read_signals_from_file()
                
                # Method 2: Get from API (uncomment and adapt)
                # signals = await self._get_signals_from_api()
                
                # Method 3: Listen to WebSocket (uncomment and adapt)
                # signals = await self._listen_websocket_signals()
                
                for signal in signals:
                    result = await self.process_signal_from_your_provider(signal)
                    print(f"📊 Signal processed: {result.get('status', 'unknown')}")
                
                await asyncio.sleep(5)  # Check for new signals every 5 seconds
                
            except KeyboardInterrupt:
                print("\n⏹️ Signal listener stopped")
                break
            except Exception as e:
                print(f"❌ Signal listener error: {e}")
                await asyncio.sleep(10)
    
    def _read_signals_from_file(self):
        """Example: Read signals from a JSON file"""
        try:
            with open('incoming_signals.json', 'r') as f:
                signals = []
                for line in f:
                    if line.strip():
                        signals.append(json.loads(line))
                
                # Clear the file after reading
                open('incoming_signals.json', 'w').close()
                
                return signals
        except FileNotFoundError:
            return []
        except Exception as e:
            print(f"❌ File reading error: {e}")
            return []
    
    async def _get_signals_from_api(self):
        """Example: Get signals from an API endpoint"""
        # Replace with your actual API endpoint
        # import aiohttp
        # async with aiohttp.ClientSession() as session:
        #     async with session.get('https://your-signal-api.com/signals') as response:
        #         if response.status == 200:
        #             data = await response.json()
        #             return data.get('signals', [])
        return []
    
    async def _listen_websocket_signals(self):
        """Example: Listen to WebSocket for signals"""
        # Replace with your actual WebSocket connection
        # import websockets
        # async with websockets.connect('wss://your-signal-websocket.com') as websocket:
        #     message = await websocket.recv()
        #     signal = json.loads(message)
        #     return [signal]
        return []
    
    def stop(self):
        """Stop the integration system"""
        self.is_running = False
        self.bot.stop()
        print("🛑 Integration system stopped")

# Example usage and testing
async def demo_integration():
    """Demonstrate how to integrate your signal provider"""
    
    # Initialize integration
    integration = SignalProviderIntegration(
        quotex_email="<EMAIL>",
        quotex_password="Uz2309##2309",
        demo_mode=True
    )
    
    try:
        # Start the integration system
        if await integration.start_integration():
            
            print("\n" + "="*60)
            print("🔗 SIGNAL INTEGRATION DEMO")
            print("="*60)
            
            # Example signals in different formats
            test_signals = [
                # Format 1: Simple format
                {
                    "pair": "EUR/USD",
                    "action": "BUY",
                    "amount": 5,
                    "expiry": "1M"
                },
                # Format 2: Detailed format
                {
                    "symbol": "GBPUSD",
                    "direction": "PUT",
                    "investment": 10.0,
                    "duration": 120,
                    "confidence": 78,
                    "strategy": "RSI_MACD"
                }
            ]
            
            # Process test signals
            for i, signal in enumerate(test_signals, 1):
                print(f"\n🔄 Processing test signal {i}/{len(test_signals)}")
                print(f"📥 Raw signal: {signal}")
                
                result = await integration.process_signal_from_your_provider(signal)
                print(f"📤 Result: {result.get('status', 'unknown')}")
                
                await asyncio.sleep(2)
            
            print("\n✅ Integration demo completed!")
            
            # Show how to create signals file for testing
            print("\n📝 To test with file-based signals:")
            print("1. Create 'incoming_signals.json' file")
            print("2. Add signals in JSON format (one per line)")
            print("3. Run integration.run_signal_listener()")
            
            # Create example signals file
            example_signals = [
                {"pair": "EUR/USD", "action": "BUY", "amount": 1, "expiry": "1M"},
                {"symbol": "GBPUSD", "direction": "PUT", "investment": 2.0, "duration": 60, "confidence": 85}
            ]
            
            with open('example_signals.json', 'w') as f:
                for signal in example_signals:
                    f.write(json.dumps(signal) + '\n')
            
            print("📄 Created 'example_signals.json' for reference")
            
        else:
            print("❌ Failed to start integration")
            
    except Exception as e:
        print(f"❌ Integration error: {e}")
    
    finally:
        integration.stop()

if __name__ == "__main__":
    print("🔗 Quotex Trading Bot Integration Guide")
    print("="*60)
    print("This guide shows how to integrate your signal provider")
    print("with the Quotex trading bot.")
    print("="*60)
    
    asyncio.run(demo_integration())
