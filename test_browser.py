#!/usr/bin/env python3
"""
Test QuotexBrowser connection
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.append('.')

from Model import Quotex<PERSON>rowser, BROWSER_AUTOMATION_AVAILABLE

async def test_browser_connection():
    """Test QuotexBrowser connection"""
    print("Testing QuotexBrowser connection...")
    
    if not BROWSER_AUTOMATION_AVAILABLE:
        print("❌ Browser automation not available")
        return
    
    print("✅ Browser automation libraries available")
    
    # Test browser instantiation
    try:
        browser = QuotexBrowser(
            email="<EMAIL>",
            password="Uz2309##2309",
            account_type="PRACTICE"
        )
        print("✅ QuotexBrowser instantiated successfully")
        
        # Test connection (this will open a browser)
        print("🌐 Testing browser connection...")
        connected = await browser.connect()
        
        if connected:
            print("✅ Browser connection successful!")
            
            # Test getting a price
            price = await browser.get_current_price("EURUSD_otc")
            print(f"💰 Current price: {price}")
            
            # Close browser
            await browser.close()
            print("✅ Browser closed successfully")
        else:
            print("❌ Browser connection failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_browser_connection())
