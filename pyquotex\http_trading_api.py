#!/usr/bin/env python3
"""
HTTP-based Quotex Trading API
Bypasses WebSocket issues by using HTTP REST API endpoints
"""

import asyncio
import time
import json
import requests
from datetime import datetime
from pyquotex.http.login import Login
from pyquotex.http.settings import Settings
from pyquotex.http.history import GetHistory
from pyquotex.expiration import get_expiration_time_quotex

class HTTPQuotexAPI:
    def __init__(self, email, password, lang="pt", demo_mode=True):
        self.email = email
        self.password = password
        self.lang = lang
        self.demo_mode = demo_mode
        self.base_url = "https://market-qx.pro"
        self.api_url = f"{self.base_url}/api/v1"
        self.session = requests.Session()
        self.is_authenticated = False
        
        # Mock API object for HTTP modules
        self.api_mock = type('obj', (object,), {
            'lang': lang,
            'https_url': self.base_url,
            'host': 'market-qx.pro',
            'resource_path': '.',
            'user_data_dir': '.',
            'username': email,
            'password': password,
            'session_data': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'cookies': None,
                'token': None
            }
        })()
        
        # Initialize HTTP modules
        self.login_module = Login(self.api_mock)
        self.settings_module = Settings(self.api_mock)
        self.history_module = GetHistory(self.api_mock)
        
    async def authenticate(self):
        """Authenticate with Quotex using HTTP"""
        try:
            print("🔐 Authenticating with Quotex...")
            
            # Perform login
            status, message = await self.login_module(self.email, self.password)
            
            if status:
                print("✅ Authentication successful!")
                
                # Get cookies from login response
                if hasattr(self.login_module, 'response') and self.login_module.response:
                    cookies = self.login_module.response.cookies
                    cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                    self.api_mock.session_data["cookies"] = cookie_string
                    
                    # Update session with cookies
                    for cookie in cookies:
                        self.session.cookies.set(cookie.name, cookie.value)
                
                self.is_authenticated = True
                return True
            else:
                print(f"❌ Authentication failed: {message}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    async def get_balance(self):
        """Get account balance via HTTP"""
        try:
            if not self.is_authenticated:
                raise Exception("Not authenticated")
            
            # Get settings which includes balance
            settings_data = self.settings_module.get_settings()
            
            if isinstance(settings_data, dict) and 'data' in settings_data:
                account_data = settings_data['data']
                if self.demo_mode:
                    return float(account_data.get('demoBalance', 0))
                else:
                    return float(account_data.get('liveBalance', 0))
            
            return 0.0
            
        except Exception as e:
            print(f"❌ Error getting balance: {e}")
            return 0.0
    
    async def place_trade_http(self, asset, direction, amount, duration):
        """
        Attempt to place trade via HTTP API
        This is experimental - trying to find the HTTP endpoint
        """
        try:
            if not self.is_authenticated:
                raise Exception("Not authenticated")
            
            # Calculate expiration time
            current_time = int(time.time())
            expiration_time = get_expiration_time_quotex(current_time, duration)
            
            # Determine option type (1 for regular, 100 for OTC)
            option_type = 100 if asset.endswith("_otc") else 1
            expiration = duration if asset.endswith("_otc") else expiration_time
            
            # Prepare trade payload (based on WebSocket format)
            payload = {
                "asset": asset,
                "amount": amount,
                "time": expiration,
                "action": direction,
                "isDemo": self.demo_mode,
                "tournamentId": 0,
                "requestId": int(time.time() * 1000),  # Generate request ID
                "optionType": option_type
            }
            
            # Try different possible HTTP endpoints
            endpoints_to_try = [
                f"{self.api_url}/orders/open",
                f"{self.api_url}/cabinets/trades/open",
                f"{self.api_url}/trades/open",
                f"{self.base_url}/orders/open",
                f"{self.base_url}/api/orders/open"
            ]
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Referer": f"{self.base_url}/{self.lang}/trade",
                "User-Agent": self.api_mock.session_data["user_agent"],
                "Cookie": self.api_mock.session_data.get("cookies", "")
            }
            
            for endpoint in endpoints_to_try:
                try:
                    print(f"🔍 Trying endpoint: {endpoint}")
                    
                    response = self.session.post(
                        endpoint,
                        json=payload,
                        headers=headers,
                        timeout=10
                    )
                    
                    print(f"📊 Response status: {response.status_code}")
                    print(f"📊 Response headers: {dict(response.headers)}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            print(f"✅ Trade placed successfully via {endpoint}")
                            print(f"📊 Response: {result}")
                            return {
                                'success': True,
                                'trade_id': result.get('id', 'unknown'),
                                'details': result,
                                'endpoint': endpoint
                            }
                        except json.JSONDecodeError:
                            print(f"⚠️ Valid response but not JSON: {response.text[:200]}")
                    
                    elif response.status_code == 404:
                        print(f"❌ Endpoint not found: {endpoint}")
                        continue
                    
                    else:
                        print(f"❌ HTTP {response.status_code}: {response.text[:200]}")
                        
                except requests.exceptions.RequestException as e:
                    print(f"❌ Request error for {endpoint}: {e}")
                    continue
            
            return {
                'success': False,
                'error': 'No working HTTP endpoint found for trading'
            }
            
        except Exception as e:
            print(f"❌ Trade placement error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_candles_http(self, asset, period=60, count=100):
        """
        Attempt to get candle data via HTTP API
        """
        try:
            if not self.is_authenticated:
                raise Exception("Not authenticated")
            
            # Try different possible candle endpoints
            endpoints_to_try = [
                f"{self.api_url}/candles/{asset}",
                f"{self.api_url}/history/candles/{asset}",
                f"{self.api_url}/market/candles/{asset}",
                f"{self.base_url}/api/candles/{asset}",
                f"{self.api_url}/charts/{asset}"
            ]
            
            params = {
                'period': period,
                'count': count,
                'timeframe': period,
                'limit': count
            }
            
            headers = {
                "Accept": "application/json",
                "Referer": f"{self.base_url}/{self.lang}/trade",
                "User-Agent": self.api_mock.session_data["user_agent"],
                "Cookie": self.api_mock.session_data.get("cookies", "")
            }
            
            for endpoint in endpoints_to_try:
                try:
                    print(f"🔍 Trying candle endpoint: {endpoint}")
                    
                    response = self.session.get(
                        endpoint,
                        params=params,
                        headers=headers,
                        timeout=10
                    )
                    
                    print(f"📊 Response status: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            print(f"✅ Candles retrieved successfully via {endpoint}")
                            return result
                        except json.JSONDecodeError:
                            print(f"⚠️ Valid response but not JSON: {response.text[:200]}")
                    
                    elif response.status_code == 404:
                        print(f"❌ Endpoint not found: {endpoint}")
                        continue
                    
                    else:
                        print(f"❌ HTTP {response.status_code}: {response.text[:200]}")
                        
                except requests.exceptions.RequestException as e:
                    print(f"❌ Request error for {endpoint}: {e}")
                    continue
            
            return []
            
        except Exception as e:
            print(f"❌ Candle data error: {e}")
            return []
    
    async def get_trade_history(self, account_type="PRACTICE", page=1):
        """Get trade history via HTTP"""
        try:
            if not self.is_authenticated:
                raise Exception("Not authenticated")
            
            history_data = await self.history_module(account_type, page)
            return history_data
            
        except Exception as e:
            print(f"❌ Error getting trade history: {e}")
            return {}

# Test the HTTP API
async def test_http_api():
    """Test the HTTP-based trading API"""
    
    api = HTTPQuotexAPI(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt",
        demo_mode=True
    )
    
    try:
        # Test authentication
        if await api.authenticate():
            print("🎉 Authentication successful!")
            
            # Test balance
            balance = await api.get_balance()
            print(f"💰 Balance: ${balance:.2f}")
            
            # Test trade history
            history = await api.get_trade_history()
            print(f"📊 Trade history: {len(history.get('data', []))} trades found")
            
            # Test candle data
            candles = await api.get_candles_http("EURUSD_otc", period=60, count=10)
            print(f"📈 Candles: {len(candles) if isinstance(candles, list) else 'No data'}")
            
            # Test trade placement (experimental)
            trade_result = await api.place_trade_http(
                asset="EURUSD_otc",
                direction="call",
                amount=1.0,
                duration=60
            )
            print(f"🎯 Trade result: {trade_result}")
            
        else:
            print("❌ Authentication failed")
            
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    asyncio.run(test_http_api())
