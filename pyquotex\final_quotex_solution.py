#!/usr/bin/env python3
"""
Final Complete Quotex Trading Solution
Combines browser automation, HTTP API, and trading functionality
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from playwright.async_api import async_playwright
from pyquotex.http.login import Login
from pyquotex.http.settings import Settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuotexTradingSolution:
    """
    Complete Quotex trading solution that combines:
    1. HTTP API for authentication and balance
    2. Browser automation for WebSocket data
    3. Trading signal processing
    4. Real-time candle data extraction
    """
    
    def __init__(self, email, password, lang="pt", demo_mode=True):
        self.email = email
        self.password = password
        self.lang = lang
        self.demo_mode = demo_mode
        self.base_url = "https://market-qx.pro"
        
        # HTTP API components
        self.api_mock = type('obj', (object,), {
            'lang': lang,
            'https_url': self.base_url,
            'host': 'market-qx.pro',
            'resource_path': '.',
            'user_data_dir': '.',
            'username': email,
            'password': password,
            'session_data': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'cookies': None,
                'token': None
            }
        })()
        
        self.login_module = Login(self.api_mock)
        self.settings_module = Settings(self.api_mock)
        
        # Data storage
        self.balance_data = {}
        self.candle_data = {}
        self.trade_history = []
        self.websocket_messages = []
        self.is_authenticated = False
        
    async def authenticate_http(self):
        """Authenticate via HTTP API"""
        try:
            print("🔐 Authenticating via HTTP...")
            
            status, message = await self.login_module(self.email, self.password)
            
            if status:
                print("✅ HTTP authentication successful!")
                
                # Get cookies
                if hasattr(self.login_module, 'response') and self.login_module.response:
                    cookies = self.login_module.response.cookies
                    cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                    self.api_mock.session_data["cookies"] = cookie_string
                
                self.is_authenticated = True
                return True
            else:
                print(f"❌ HTTP authentication failed: {message}")
                return False
                
        except Exception as e:
            print(f"❌ HTTP authentication error: {e}")
            return False
    
    async def get_balance_http(self):
        """Get balance via HTTP API"""
        try:
            if not self.is_authenticated:
                await self.authenticate_http()
            
            settings_data = self.settings_module.get_settings()
            
            if isinstance(settings_data, dict) and 'data' in settings_data:
                account_data = settings_data['data']
                
                self.balance_data = {
                    'live_balance': float(account_data.get('liveBalance', 0)),
                    'demo_balance': float(account_data.get('demoBalance', 0)),
                    'currency': account_data.get('currencyCode', 'USD'),
                    'is_demo': self.demo_mode,
                    'user_id': account_data.get('nickname', 'Unknown'),
                    'email': account_data.get('email', 'Unknown'),
                    'country': account_data.get('countryName', 'Unknown')
                }
                
                return self.balance_data
            
            return None
            
        except Exception as e:
            print(f"❌ Error getting balance: {e}")
            return None
    
    async def start_browser_websocket_session(self, duration=30):
        """Start browser session to capture WebSocket data"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                viewport={'width': 1920, 'height': 1080}
            )
            
            page = await context.new_page()
            
            # Capture WebSocket data
            websocket_data = []
            candle_data = {}
            balance_updates = []
            
            def handle_websocket(ws):
                print(f"🔌 WebSocket connected: {ws.url}")
                
                def on_framereceived(payload):
                    try:
                        # Parse WebSocket messages
                        if isinstance(payload, bytes):
                            payload = payload.decode('utf-8')
                        
                        # Extract candle data
                        if 'GBPAUD_otc' in str(payload) or 'EURUSD_otc' in str(payload):
                            if 'history/list' in str(payload):
                                # This is historical candle data
                                self._parse_candle_data(payload)
                            elif 'quotes/stream' in str(payload):
                                # This is real-time price data
                                self._parse_price_data(payload)
                        
                        # Extract balance data
                        if 'liveBalance' in str(payload) or 'demoBalance' in str(payload):
                            self._parse_balance_data(payload)
                        
                        websocket_data.append({
                            'timestamp': time.time(),
                            'data': payload
                        })
                        
                    except Exception as e:
                        logger.error(f"WebSocket parsing error: {e}")
                
                ws.on("framereceived", on_framereceived)
            
            page.on("websocket", handle_websocket)
            
            try:
                # Navigate and login
                print("🌐 Opening Quotex trading page...")
                await page.goto(f"{self.base_url}/{self.lang}/trade", timeout=60000)
                
                # Wait for page to load and WebSocket to connect
                await page.wait_for_load_state("networkidle", timeout=30000)
                
                print(f"⏳ Capturing WebSocket data for {duration} seconds...")
                await asyncio.sleep(duration)
                
                # Save captured data
                session_data = {
                    'websocket_messages': websocket_data,
                    'candle_data': self.candle_data,
                    'balance_data': self.balance_data,
                    'timestamp': time.time()
                }
                
                with open('websocket_session_data.json', 'w') as f:
                    json.dump(session_data, f, indent=2, default=str)
                
                print(f"💾 Captured {len(websocket_data)} WebSocket messages")
                print(f"📊 Captured candle data for {len(self.candle_data)} assets")
                
                return session_data
                
            except Exception as e:
                print(f"❌ Browser session error: {e}")
                return None
            
            finally:
                await browser.close()
    
    def _parse_candle_data(self, payload):
        """Parse candle data from WebSocket messages"""
        try:
            # Extract asset and candle information
            if 'history' in str(payload) and 'candles' in str(payload):
                # This contains historical candle data
                print(f"📊 Parsing candle data from: {str(payload)[:100]}...")
                # Add parsing logic here based on the actual format
                
        except Exception as e:
            logger.error(f"Candle parsing error: {e}")
    
    def _parse_price_data(self, payload):
        """Parse real-time price data"""
        try:
            # Extract real-time price updates
            if 'quotes/stream' in str(payload):
                print(f"💹 Real-time price: {str(payload)[:100]}...")
                
        except Exception as e:
            logger.error(f"Price parsing error: {e}")
    
    def _parse_balance_data(self, payload):
        """Parse balance updates"""
        try:
            if 'liveBalance' in str(payload):
                print(f"💰 Balance update: {str(payload)[:100]}...")
                
        except Exception as e:
            logger.error(f"Balance parsing error: {e}")
    
    async def process_trading_signal(self, signal):
        """
        Process a trading signal
        
        Args:
            signal (dict): Trading signal with format:
                {
                    'asset': 'EURUSD_otc',
                    'direction': 'call',  # 'call' or 'put'
                    'amount': 10.0,
                    'duration': 60,
                    'confidence': 0.85
                }
        """
        try:
            print(f"\n🎯 Processing Trading Signal:")
            print(f"   Asset: {signal.get('asset', 'Unknown')}")
            print(f"   Direction: {signal.get('direction', 'Unknown').upper()}")
            print(f"   Amount: ${signal.get('amount', 0):.2f}")
            print(f"   Duration: {signal.get('duration', 0)}s")
            
            # Get current balance
            balance = await self.get_balance_http()
            
            if balance:
                current_balance = balance['demo_balance'] if self.demo_mode else balance['live_balance']
                
                if current_balance >= signal['amount']:
                    print(f"✅ Sufficient balance: ${current_balance:.2f}")
                    
                    # Since WebSocket trading requires browser automation,
                    # provide manual trading instructions
                    print(f"\n📋 MANUAL TRADING INSTRUCTIONS:")
                    print(f"   1. Open: {self.base_url}/{self.lang}/trade")
                    print(f"   2. Select asset: {signal['asset']}")
                    print(f"   3. Set amount: ${signal['amount']:.2f}")
                    print(f"   4. Set duration: {signal['duration']}s")
                    print(f"   5. Click {'HIGHER (CALL)' if signal['direction'] == 'call' else 'LOWER (PUT)'}")
                    print(f"   6. Execute trade!")
                    
                    # Log the signal
                    trade_log = {
                        'timestamp': time.time(),
                        'signal': signal,
                        'balance_before': current_balance,
                        'status': 'manual_execution_required'
                    }
                    
                    self.trade_history.append(trade_log)
                    
                    return {
                        'success': True,
                        'status': 'manual_execution_required',
                        'balance': current_balance,
                        'instructions': 'Manual trading instructions provided'
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Insufficient balance. Required: ${signal["amount"]:.2f}, Available: ${current_balance:.2f}'
                    }
            else:
                return {
                    'success': False,
                    'error': 'Could not retrieve balance'
                }
                
        except Exception as e:
            logger.error(f"Signal processing error: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def display_status(self):
        """Display current system status"""
        print("\n" + "="*60)
        print("📊 QUOTEX TRADING SOLUTION STATUS")
        print("="*60)
        
        if self.balance_data:
            print(f"👤 User: {self.balance_data.get('user_id', 'Unknown')}")
            print(f"📧 Email: {self.balance_data.get('email', 'Unknown')}")
            print(f"🌍 Country: {self.balance_data.get('country', 'Unknown')}")
            print(f"💰 Live Balance: ${self.balance_data.get('live_balance', 0):.2f}")
            print(f"🔵 Demo Balance: ${self.balance_data.get('demo_balance', 0):.2f}")
            print(f"💼 Current Mode: {'Demo' if self.demo_mode else 'Live'}")
        
        print(f"📨 WebSocket Messages: {len(self.websocket_messages)}")
        print(f"📊 Candle Data Assets: {len(self.candle_data)}")
        print(f"🎯 Processed Signals: {len(self.trade_history)}")
        print("="*60)

# Example usage and testing
async def main():
    """Main function demonstrating the complete solution"""
    
    # Initialize the solution
    solution = QuotexTradingSolution(
        email="<EMAIL>",
        password="Uz2309##2309",
        lang="pt",
        demo_mode=True
    )
    
    try:
        print("🚀 Starting Complete Quotex Trading Solution...")
        
        # Step 1: Test HTTP authentication and balance
        print("\n📋 Step 1: HTTP Authentication & Balance")
        balance = await solution.get_balance_http()
        
        if balance:
            solution.display_status()
        else:
            print("❌ Failed to get balance")
            return
        
        # Step 2: Process sample trading signals
        print("\n📋 Step 2: Processing Trading Signals")
        
        sample_signals = [
            {
                'asset': 'EURUSD_otc',
                'direction': 'call',
                'amount': 1.0,
                'duration': 60,
                'confidence': 0.85
            },
            {
                'asset': 'GBPUSD_otc',
                'direction': 'put',
                'amount': 2.0,
                'duration': 120,
                'confidence': 0.78
            }
        ]
        
        for i, signal in enumerate(sample_signals, 1):
            print(f"\n🔄 Processing signal {i}/{len(sample_signals)}")
            result = await solution.process_trading_signal(signal)
            print(f"📊 Result: {result.get('status', 'unknown')}")
            
            if i < len(sample_signals):
                await asyncio.sleep(2)
        
        # Step 3: Optional - Start browser WebSocket session
        print("\n📋 Step 3: WebSocket Data Capture (Optional)")
        print("💡 To capture real-time WebSocket data, uncomment the next line:")
        print("   # websocket_data = await solution.start_browser_websocket_session(30)")
        
        # Uncomment to capture WebSocket data:
        # websocket_data = await solution.start_browser_websocket_session(30)
        
        print("\n✅ Complete solution demonstration finished!")
        print("\n📝 Summary:")
        print("   ✅ HTTP authentication working")
        print("   ✅ Balance retrieval working")
        print("   ✅ Signal processing working")
        print("   ✅ Manual trading instructions provided")
        print("   ✅ WebSocket data capture available")
        
        print("\n🎯 Next Steps for Full Automation:")
        print("   1. Use browser automation for trade execution")
        print("   2. Implement WebSocket message parsing")
        print("   3. Add real-time candle data processing")
        print("   4. Connect with your signal provider")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
