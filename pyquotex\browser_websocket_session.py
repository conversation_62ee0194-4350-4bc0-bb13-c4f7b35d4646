#!/usr/bin/env python3
"""
Browser-based WebSocket Session Extractor
Uses browser automation to establish WebSocket connection and extract session data
"""

import asyncio
import json
import time
from playwright.async_api import async_playwright

class BrowserWebSocketSession:
    """Extract WebSocket session data using browser automation"""
    
    def __init__(self, email, password, headless=False):
        self.email = email
        self.password = password
        self.headless = headless
        self.websocket_messages = []
        self.session_data = {}
        
    async def extract_websocket_session(self):
        """Extract WebSocket session data by automating browser"""
        async with async_playwright() as p:
            # Launch browser with realistic settings
            browser = await p.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Create context with realistic settings
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                viewport={'width': 1920, 'height': 1080},
                locale='en-US',
                timezone_id='America/New_York'
            )
            
            page = await context.new_page()
            
            # Intercept WebSocket connections
            websocket_data = []
            
            def handle_websocket(ws):
                print(f"🔌 WebSocket connection established: {ws.url}")
                
                def on_framesent(payload):
                    print(f"📤 WebSocket sent: {payload}")
                    websocket_data.append({'type': 'sent', 'data': payload})
                
                def on_framereceived(payload):
                    print(f"📥 WebSocket received: {payload}")
                    websocket_data.append({'type': 'received', 'data': payload})
                
                def on_close():
                    print("🔌 WebSocket closed")
                
                ws.on("framesent", on_framesent)
                ws.on("framereceived", on_framereceived)
                ws.on("close", on_close)
            
            page.on("websocket", handle_websocket)
            
            try:
                print("🌐 Navigating to Quotex login page...")
                await page.goto("https://market-qx.pro/pt/sign-in/", timeout=60000)
                
                # Wait for page to load
                await page.wait_for_load_state("networkidle", timeout=30000)
                await asyncio.sleep(3)
                
                print("🔍 Looking for login form...")

                # Try different approaches to find and activate login form
                try:
                    # Try to click login tab/button
                    login_selectors = [
                        'a[data-value="1"]',
                        '.modal-sign__tab[data-value="1"]',
                        '[data-tab="login"]',
                        '.login-tab',
                        'button:has-text("Login")',
                        'a:has-text("Login")'
                    ]

                    for selector in login_selectors:
                        try:
                            element = await page.wait_for_selector(selector, timeout=3000)
                            if element:
                                await element.click()
                                await asyncio.sleep(2)
                                print(f"✅ Clicked login element: {selector}")
                                break
                        except:
                            continue
                except:
                    pass

                # Try different email input selectors
                email_input = None
                email_selectors = [
                    'input[name="email"]',
                    '#emailInput',
                    'input[type="email"]',
                    '.modal-sign__input-value[name="email"]'
                ]

                for selector in email_selectors:
                    try:
                        email_input = await page.wait_for_selector(selector, timeout=5000, state='visible')
                        if email_input:
                            print(f"✅ Found email input: {selector}")
                            break
                    except:
                        continue

                # Try different password input selectors
                password_input = None
                password_selectors = [
                    'input[name="password"]',
                    'input[type="password"]',
                    '.modal-sign__input-value[name="password"]'
                ]

                for selector in password_selectors:
                    try:
                        password_input = await page.wait_for_selector(selector, timeout=5000, state='visible')
                        if password_input:
                            print(f"✅ Found password input: {selector}")
                            break
                    except:
                        continue
                
                if email_input and password_input:
                    print("📝 Filling login credentials...")
                    
                    await email_input.fill(self.email)
                    await password_input.fill(self.password)
                    await asyncio.sleep(2)
                    
                    # Submit form
                    print("🚀 Submitting login...")
                    await password_input.press('Enter')
                    
                    # Wait for login and redirect
                    print("⏳ Waiting for login and redirect...")
                    await page.wait_for_load_state("networkidle", timeout=30000)
                    
                    current_url = page.url
                    print(f"📍 Current URL: {current_url}")
                    
                    if "trade" in current_url or "cabinet" in current_url:
                        print("✅ Login successful! Now waiting for WebSocket connection...")
                        
                        # Wait for WebSocket connections to establish
                        await asyncio.sleep(10)
                        
                        # Try to trigger WebSocket activity
                        print("🎯 Trying to trigger WebSocket activity...")
                        
                        # Look for trading interface elements
                        try:
                            # Try to find and interact with trading elements
                            asset_selector = await page.query_selector('[data-asset], .asset-item, .instrument-item')
                            if asset_selector:
                                await asset_selector.click()
                                await asyncio.sleep(2)
                        except:
                            pass
                        
                        # Wait more for WebSocket messages
                        await asyncio.sleep(15)
                        
                        # Get all cookies including Cloudflare ones
                        cookies = await context.cookies()
                        cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
                        
                        # Get local storage and session storage
                        local_storage = await page.evaluate("() => JSON.stringify(localStorage)")
                        session_storage = await page.evaluate("() => JSON.stringify(sessionStorage)")
                        
                        # Extract session data
                        self.session_data = {
                            'cookies': cookie_dict,
                            'local_storage': json.loads(local_storage) if local_storage != '{}' else {},
                            'session_storage': json.loads(session_storage) if session_storage != '{}' else {},
                            'user_agent': await page.evaluate("navigator.userAgent"),
                            'url': current_url,
                            'websocket_messages': websocket_data,
                            'timestamp': time.time()
                        }
                        
                        print(f"🍪 Extracted {len(cookies)} cookies")
                        print(f"📨 Captured {len(websocket_data)} WebSocket messages")
                        
                        # Save session data
                        with open('browser_session_data.json', 'w') as f:
                            json.dump(self.session_data, f, indent=2)
                        
                        print("💾 Session data saved to browser_session_data.json")
                        
                        return self.session_data
                    else:
                        print("❌ Login failed - not redirected to trading page")
                        return None
                else:
                    print("❌ Login form not found")
                    return None
                    
            except Exception as e:
                print(f"❌ Browser automation error: {e}")
                import traceback
                traceback.print_exc()
                return None
            
            finally:
                await browser.close()

async def test_browser_session():
    """Test browser-based session extraction"""
    
    extractor = BrowserWebSocketSession(
        email="<EMAIL>",
        password="Uz2309##2309",
        headless=False  # Set to True for headless mode
    )
    
    print("🚀 Starting browser-based WebSocket session extraction...")
    
    session_data = await extractor.extract_websocket_session()
    
    if session_data:
        print("\n🎉 Session extraction successful!")
        print(f"📊 Summary:")
        print(f"   Cookies: {len(session_data.get('cookies', {}))}")
        print(f"   WebSocket messages: {len(session_data.get('websocket_messages', []))}")
        print(f"   Local storage items: {len(session_data.get('local_storage', {}))}")
        
        # Show some key cookies
        cookies = session_data.get('cookies', {})
        important_cookies = ['laravel_session', '__cf_bm', 'XSRF-TOKEN']
        
        print(f"\n🔑 Important cookies:")
        for cookie_name in important_cookies:
            if cookie_name in cookies:
                value = cookies[cookie_name]
                print(f"   {cookie_name}: {value[:50]}...")
        
        # Show WebSocket messages
        ws_messages = session_data.get('websocket_messages', [])
        if ws_messages:
            print(f"\n📨 WebSocket messages sample:")
            for i, msg in enumerate(ws_messages[:5]):  # Show first 5 messages
                print(f"   {i+1}. {msg['type']}: {str(msg['data'])[:100]}...")
        
        return session_data
    else:
        print("❌ Session extraction failed")
        return None

if __name__ == "__main__":
    result = asyncio.run(test_browser_session())
    
    if result:
        print("\n✅ Ready to use extracted session data for WebSocket connection!")
        print("💡 Next step: Use the session data to establish WebSocket connection programmatically")
    else:
        print("❌ Failed to extract session data")
